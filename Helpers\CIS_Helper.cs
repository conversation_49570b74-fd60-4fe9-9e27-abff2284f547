﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Zambia.Invoice.Models;
using Zambia.Invoice.Models.Context;

namespace Zambia.Invoice.Helpers
{
    public class CIS_Helper
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public CIS_Helper(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<string> CreateCISInvoiceNumberAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            
            // Get the highest existing CIS number or start from 1
            var lastCIS = await context.CIS_Invoices
                .OrderByDescending(c => c.CreatedAt)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastCIS != null && int.TryParse(lastCIS.CIS, out int lastNumber))
            {
                nextNumber = lastNumber + 1;
            }

            string cisNumber = nextNumber.ToString().PadLeft(8, '0'); // Format as 8-digit number

            // Save to database
            var cisInvoice = new CIS_InvoiceModel
            {
                CIS = cisNumber,
                CreatedAt = DateTime.UtcNow
            };

            context.CIS_Invoices.Add(cisInvoice);
            await context.SaveChangesAsync();

            return cisNumber;
        }

        public async Task RemoveCISInvoiceNumberAsync(string cisNumber)
        {
            using var context = _contextFactory.CreateDbContext();
            
            var cisInvoice = await context.CIS_Invoices
                .FirstOrDefaultAsync(c => c.CIS == cisNumber);

            if (cisInvoice != null)
            {
                context.CIS_Invoices.Remove(cisInvoice);
                await context.SaveChangesAsync();
            }
        }

        public async Task SaveSuccessfulInvoiceDB_Async(string invoiceId, string cisNumber, InvoiceResponseModel response)
        {
            if (String.IsNullOrEmpty(invoiceId))
            {
                invoiceId = cisNumber;
            }

            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // Create InvoiceResponseEntity
                var invoiceResponseEntity = new InvoiceResponseEntity
                {
                    Id = Guid.NewGuid(),
                    resultCd = response.resultCd ?? string.Empty,
                    resultMsg = response.resultMsg ?? string.Empty,
                    resultDt = response.resultDt ?? string.Empty,
                    rcptNo = response.data?.rcptNo ?? 0,
                    intrlData = response.data?.intrlData ?? string.Empty,
                    rcptSign = response.data?.rcptSign ?? string.Empty,
                    vsdcRcptPbctDate = response.data?.vsdcRcptPbctDate ?? string.Empty,
                    sdcId = response.data?.sdcId ?? string.Empty,
                    mrcNo = response.data?.mrcNo ?? string.Empty,
                    qrCodeUrl = response.data?.qrCodeUrl ?? string.Empty,
                    CIS = cisNumber,
                    invoiceId = invoiceId,
                    CreatedAt = DateTime.UtcNow,

                    //convert qrCodeUrl to a QR image
                    qrCodeImage = QR_Helper.GenerateQRCodeImageWithLogo(response.data?.qrCodeUrl ?? string.Empty)

                };

                context.InvoiceResponses.Add(invoiceResponseEntity);
                await context.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error saving successful invoice: {ex.Message}", ex);
            }
        }
    }
}
