﻿namespace Zambia.Invoice.Forms.ItemForms
{
    partial class frm_AddItem
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            lbl_Tpin = new Label();
            lbl_bhfId = new Label();
            lbl_itemClsCd = new Label();
            lbl_itemTyCd = new Label();
            lbl_qtyUnitCd = new Label();
            lbl_pkgUnitCd = new Label();
            lbl_orgnNatCd = new Label();
            lbl_itemNm = new Label();
            lbl_iplCatCd = new Label();
            lbl_vatCatCd = new Label();
            lbl_regrNm = new Label();
            lbl_useYn = new Label();
            lbl_modrNm = new Label();
            lbl_regrId = new Label();
            lbl_modrId = new Label();
            lbl_exciseTxCatCd = new Label();
            lbl_tlCatCd = new Label();
            grp_Required = new GroupBox();
            cmb_qtyUnitCd = new ComboBox();
            cmb_pkgUnitCd = new ComboBox();
            cmb_orgnNatCd = new ComboBox();
            cmb_itemTyCd = new ComboBox();
            llb_itemClsCd = new LinkLabel();
            txt_itemCd = new TextBox();
            cmb_useYn = new ComboBox();
            txt_itemNm = new TextBox();
            lbl_itemCd = new Label();
            txt_itemClsCd = new TextBox();
            txt_regrNm = new TextBox();
            txt_bhfId = new TextBox();
            txt_modrId = new TextBox();
            txt_Tpin = new TextBox();
            txt_regrId = new TextBox();
            txt_modrNm = new TextBox();
            cmb_vatCatCd = new ComboBox();
            lbl_rrp = new Label();
            txt_itemStdNm = new TextBox();
            lbl_svcChargeYn = new Label();
            txt_btchNo = new TextBox();
            lbl_manufacturerItemCd = new Label();
            txt_bcd = new TextBox();
            lbl_rentalYn = new Label();
            txt_dftPrc = new TextBox();
            lbl_manufactuterTpin = new Label();
            lbl_sftyQty = new Label();
            lbl_addInfo = new Label();
            txt_manufactuterTpin = new TextBox();
            lbl_dftPrc = new Label();
            txt_manufacturerItemCd = new TextBox();
            lbl_bcd = new Label();
            txt_rrp = new TextBox();
            lbl_btchNo = new Label();
            lbl_itemStdNm = new Label();
            rtb_addInfo = new RichTextBox();
            numericUpDown1 = new NumericUpDown();
            grp_Optional = new GroupBox();
            cmb_svcChargeYn = new ComboBox();
            cmb_rentalYn = new ComboBox();
            toolTip_USNPSC = new ToolTip(components);
            grp_PossibleNull = new GroupBox();
            cmb_exciseTxCatCd = new ComboBox();
            cmb_tlCatCd = new ComboBox();
            cmb_iplCatCd = new ComboBox();
            btn_AddItem = new Button();
            grp_Required.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown1).BeginInit();
            grp_Optional.SuspendLayout();
            grp_PossibleNull.SuspendLayout();
            SuspendLayout();
            // 
            // lbl_Tpin
            // 
            lbl_Tpin.AutoSize = true;
            lbl_Tpin.Location = new Point(25, 30);
            lbl_Tpin.Name = "lbl_Tpin";
            lbl_Tpin.Size = new Size(179, 15);
            lbl_Tpin.TabIndex = 0;
            lbl_Tpin.Text = "Taxpayer’s identification number";
            // 
            // lbl_bhfId
            // 
            lbl_bhfId.AutoSize = true;
            lbl_bhfId.Location = new Point(25, 61);
            lbl_bhfId.Name = "lbl_bhfId";
            lbl_bhfId.Size = new Size(226, 15);
            lbl_bhfId.TabIndex = 1;
            lbl_bhfId.Text = "Taxpayer branch (store) location identifier";
            // 
            // lbl_itemClsCd
            // 
            lbl_itemClsCd.AutoSize = true;
            lbl_itemClsCd.Location = new Point(24, 90);
            lbl_itemClsCd.Name = "lbl_itemClsCd";
            lbl_itemClsCd.Size = new Size(178, 15);
            lbl_itemClsCd.TabIndex = 2;
            lbl_itemClsCd.Text = "USNPSC item classification code";
            // 
            // lbl_itemTyCd
            // 
            lbl_itemTyCd.AutoSize = true;
            lbl_itemTyCd.Location = new Point(25, 300);
            lbl_itemTyCd.Name = "lbl_itemTyCd";
            lbl_itemTyCd.Size = new Size(102, 15);
            lbl_itemTyCd.TabIndex = 3;
            lbl_itemTyCd.Text = "Item product type";
            // 
            // lbl_qtyUnitCd
            // 
            lbl_qtyUnitCd.AutoSize = true;
            lbl_qtyUnitCd.Location = new Point(25, 391);
            lbl_qtyUnitCd.Name = "lbl_qtyUnitCd";
            lbl_qtyUnitCd.Size = new Size(107, 15);
            lbl_qtyUnitCd.TabIndex = 7;
            lbl_qtyUnitCd.Text = "Item quantity code";
            // 
            // lbl_pkgUnitCd
            // 
            lbl_pkgUnitCd.AutoSize = true;
            lbl_pkgUnitCd.Location = new Point(25, 362);
            lbl_pkgUnitCd.Name = "lbl_pkgUnitCd";
            lbl_pkgUnitCd.Size = new Size(142, 15);
            lbl_pkgUnitCd.TabIndex = 6;
            lbl_pkgUnitCd.Text = "Item packaging unit code";
            // 
            // lbl_orgnNatCd
            // 
            lbl_orgnNatCd.AutoSize = true;
            lbl_orgnNatCd.Location = new Point(24, 331);
            lbl_orgnNatCd.Name = "lbl_orgnNatCd";
            lbl_orgnNatCd.Size = new Size(98, 15);
            lbl_orgnNatCd.TabIndex = 5;
            lbl_orgnNatCd.Text = "Country of origin";
            lbl_orgnNatCd.Click += lbl_orgnNatCd_Click;
            // 
            // lbl_itemNm
            // 
            lbl_itemNm.AutoSize = true;
            lbl_itemNm.Location = new Point(25, 119);
            lbl_itemNm.Name = "lbl_itemNm";
            lbl_itemNm.Size = new Size(188, 15);
            lbl_itemNm.TabIndex = 4;
            lbl_itemNm.Text = "Name of the item being registered";
            // 
            // lbl_iplCatCd
            // 
            lbl_iplCatCd.AutoSize = true;
            lbl_iplCatCd.Location = new Point(21, 65);
            lbl_iplCatCd.Name = "lbl_iplCatCd";
            lbl_iplCatCd.Size = new Size(226, 15);
            lbl_iplCatCd.TabIndex = 10;
            lbl_iplCatCd.Text = " IPL category code of the registered item. ";
            // 
            // lbl_vatCatCd
            // 
            lbl_vatCatCd.AutoSize = true;
            lbl_vatCatCd.Location = new Point(23, 36);
            lbl_vatCatCd.Name = "lbl_vatCatCd";
            lbl_vatCatCd.Size = new Size(213, 15);
            lbl_vatCatCd.TabIndex = 9;
            lbl_vatCatCd.Text = "VAT category code of a registered item.";
            // 
            // lbl_regrNm
            // 
            lbl_regrNm.AutoSize = true;
            lbl_regrNm.Location = new Point(24, 148);
            lbl_regrNm.Name = "lbl_regrNm";
            lbl_regrNm.Size = new Size(225, 15);
            lbl_regrNm.TabIndex = 22;
            lbl_regrNm.Text = "Username of the user registering the item";
            // 
            // lbl_useYn
            // 
            lbl_useYn.AutoSize = true;
            lbl_useYn.Location = new Point(26, 424);
            lbl_useYn.Name = "lbl_useYn";
            lbl_useYn.Size = new Size(189, 15);
            lbl_useYn.TabIndex = 20;
            lbl_useYn.Text = "Usage status of the registered item";
            // 
            // lbl_modrNm
            // 
            lbl_modrNm.AutoSize = true;
            lbl_modrNm.Location = new Point(23, 208);
            lbl_modrNm.Name = "lbl_modrNm";
            lbl_modrNm.Size = new Size(224, 15);
            lbl_modrNm.TabIndex = 28;
            lbl_modrNm.Text = "Username of the user modifying the item";
            // 
            // lbl_regrId
            // 
            lbl_regrId.AutoSize = true;
            lbl_regrId.Location = new Point(24, 179);
            lbl_regrId.Name = "lbl_regrId";
            lbl_regrId.Size = new Size(208, 15);
            lbl_regrId.TabIndex = 27;
            lbl_regrId.Text = "User id of the user registering the item";
            // 
            // lbl_modrId
            // 
            lbl_modrId.AutoSize = true;
            lbl_modrId.Location = new Point(23, 239);
            lbl_modrId.Name = "lbl_modrId";
            lbl_modrId.Size = new Size(207, 15);
            lbl_modrId.TabIndex = 26;
            lbl_modrId.Text = "User id of the user modifying the item";
            // 
            // lbl_exciseTxCatCd
            // 
            lbl_exciseTxCatCd.AutoSize = true;
            lbl_exciseTxCatCd.Location = new Point(24, 128);
            lbl_exciseTxCatCd.Name = "lbl_exciseTxCatCd";
            lbl_exciseTxCatCd.Size = new Size(232, 15);
            lbl_exciseTxCatCd.TabIndex = 24;
            lbl_exciseTxCatCd.Text = "Excise category code of the registered item";
            // 
            // lbl_tlCatCd
            // 
            lbl_tlCatCd.AutoSize = true;
            lbl_tlCatCd.Location = new Point(24, 97);
            lbl_tlCatCd.Name = "lbl_tlCatCd";
            lbl_tlCatCd.Size = new Size(217, 15);
            lbl_tlCatCd.TabIndex = 23;
            lbl_tlCatCd.Text = "TL category code of the registered item.";
            // 
            // grp_Required
            // 
            grp_Required.Controls.Add(cmb_qtyUnitCd);
            grp_Required.Controls.Add(cmb_pkgUnitCd);
            grp_Required.Controls.Add(cmb_orgnNatCd);
            grp_Required.Controls.Add(lbl_regrNm);
            grp_Required.Controls.Add(cmb_itemTyCd);
            grp_Required.Controls.Add(lbl_regrId);
            grp_Required.Controls.Add(llb_itemClsCd);
            grp_Required.Controls.Add(txt_itemCd);
            grp_Required.Controls.Add(cmb_useYn);
            grp_Required.Controls.Add(lbl_modrId);
            grp_Required.Controls.Add(txt_itemNm);
            grp_Required.Controls.Add(lbl_itemCd);
            grp_Required.Controls.Add(lbl_modrNm);
            grp_Required.Controls.Add(txt_itemClsCd);
            grp_Required.Controls.Add(txt_regrNm);
            grp_Required.Controls.Add(txt_bhfId);
            grp_Required.Controls.Add(txt_modrId);
            grp_Required.Controls.Add(txt_Tpin);
            grp_Required.Controls.Add(txt_regrId);
            grp_Required.Controls.Add(lbl_Tpin);
            grp_Required.Controls.Add(txt_modrNm);
            grp_Required.Controls.Add(lbl_bhfId);
            grp_Required.Controls.Add(lbl_itemClsCd);
            grp_Required.Controls.Add(lbl_itemTyCd);
            grp_Required.Controls.Add(lbl_itemNm);
            grp_Required.Controls.Add(lbl_useYn);
            grp_Required.Controls.Add(lbl_orgnNatCd);
            grp_Required.Controls.Add(lbl_pkgUnitCd);
            grp_Required.Controls.Add(lbl_qtyUnitCd);
            grp_Required.Location = new Point(22, 12);
            grp_Required.Name = "grp_Required";
            grp_Required.Size = new Size(424, 466);
            grp_Required.TabIndex = 29;
            grp_Required.TabStop = false;
            grp_Required.Text = "*Required";
            // 
            // cmb_qtyUnitCd
            // 
            cmb_qtyUnitCd.FormattingEnabled = true;
            cmb_qtyUnitCd.Location = new Point(288, 381);
            cmb_qtyUnitCd.Name = "cmb_qtyUnitCd";
            cmb_qtyUnitCd.Size = new Size(100, 23);
            cmb_qtyUnitCd.TabIndex = 61;
            // 
            // cmb_pkgUnitCd
            // 
            cmb_pkgUnitCd.FormattingEnabled = true;
            cmb_pkgUnitCd.Location = new Point(288, 352);
            cmb_pkgUnitCd.Name = "cmb_pkgUnitCd";
            cmb_pkgUnitCd.Size = new Size(100, 23);
            cmb_pkgUnitCd.TabIndex = 61;
            // 
            // cmb_orgnNatCd
            // 
            cmb_orgnNatCd.FormattingEnabled = true;
            cmb_orgnNatCd.Location = new Point(288, 323);
            cmb_orgnNatCd.Name = "cmb_orgnNatCd";
            cmb_orgnNatCd.Size = new Size(100, 23);
            cmb_orgnNatCd.TabIndex = 61;
            // 
            // cmb_itemTyCd
            // 
            cmb_itemTyCd.FormattingEnabled = true;
            cmb_itemTyCd.Location = new Point(288, 294);
            cmb_itemTyCd.Name = "cmb_itemTyCd";
            cmb_itemTyCd.Size = new Size(100, 23);
            cmb_itemTyCd.TabIndex = 61;
            // 
            // llb_itemClsCd
            // 
            llb_itemClsCd.AutoSize = true;
            llb_itemClsCd.Location = new Point(206, 90);
            llb_itemClsCd.Name = "llb_itemClsCd";
            llb_itemClsCd.Size = new Size(70, 15);
            llb_itemClsCd.TabIndex = 62;
            llb_itemClsCd.TabStop = true;
            llb_itemClsCd.Text = "Class Codes";
            llb_itemClsCd.LinkClicked += llb_itemClsCd_LinkClicked;
            // 
            // txt_itemCd
            // 
            txt_itemCd.Enabled = false;
            txt_itemCd.Location = new Point(287, 262);
            txt_itemCd.MaxLength = 100;
            txt_itemCd.Name = "txt_itemCd";
            txt_itemCd.Size = new Size(100, 23);
            txt_itemCd.TabIndex = 47;
            // 
            // cmb_useYn
            // 
            cmb_useYn.FormattingEnabled = true;
            cmb_useYn.Location = new Point(289, 416);
            cmb_useYn.Name = "cmb_useYn";
            cmb_useYn.Size = new Size(100, 23);
            cmb_useYn.TabIndex = 61;
            cmb_useYn.SelectedIndexChanged += cmb_useYn_SelectedIndexChanged;
            // 
            // txt_itemNm
            // 
            txt_itemNm.Location = new Point(289, 111);
            txt_itemNm.MaxLength = 200;
            txt_itemNm.Name = "txt_itemNm";
            txt_itemNm.Size = new Size(100, 23);
            txt_itemNm.TabIndex = 33;
            txt_itemNm.Text = "Test Product";
            // 
            // lbl_itemCd
            // 
            lbl_itemCd.AutoSize = true;
            lbl_itemCd.Location = new Point(23, 270);
            lbl_itemCd.Name = "lbl_itemCd";
            lbl_itemCd.Size = new Size(156, 15);
            lbl_itemCd.TabIndex = 46;
            lbl_itemCd.Text = "Item Code (Auto Generated)";
            // 
            // txt_itemClsCd
            // 
            txt_itemClsCd.Location = new Point(288, 82);
            txt_itemClsCd.MaxLength = 10;
            txt_itemClsCd.Name = "txt_itemClsCd";
            txt_itemClsCd.Size = new Size(100, 23);
            txt_itemClsCd.TabIndex = 31;
            txt_itemClsCd.Text = "55101500";
            txt_itemClsCd.TextChanged += txt_itemClsCd_TextChanged;
            // 
            // txt_regrNm
            // 
            txt_regrNm.Location = new Point(288, 140);
            txt_regrNm.MaxLength = 60;
            txt_regrNm.Name = "txt_regrNm";
            txt_regrNm.Size = new Size(100, 23);
            txt_regrNm.TabIndex = 42;
            txt_regrNm.Text = "Jacq";
            txt_regrNm.TextChanged += txt_regrNm_TextChanged;
            // 
            // txt_bhfId
            // 
            txt_bhfId.Location = new Point(288, 53);
            txt_bhfId.MaxLength = 3;
            txt_bhfId.Name = "txt_bhfId";
            txt_bhfId.Size = new Size(100, 23);
            txt_bhfId.TabIndex = 30;
            txt_bhfId.Text = "000";
            // 
            // txt_modrId
            // 
            txt_modrId.Location = new Point(287, 231);
            txt_modrId.MaxLength = 20;
            txt_modrId.Name = "txt_modrId";
            txt_modrId.Size = new Size(100, 23);
            txt_modrId.TabIndex = 45;
            txt_modrId.Text = "1";
            // 
            // txt_Tpin
            // 
            txt_Tpin.Location = new Point(288, 22);
            txt_Tpin.MaxLength = 10;
            txt_Tpin.Name = "txt_Tpin";
            txt_Tpin.Size = new Size(100, 23);
            txt_Tpin.TabIndex = 29;
            txt_Tpin.Text = "1002087869";
            // 
            // txt_regrId
            // 
            txt_regrId.Location = new Point(287, 171);
            txt_regrId.MaxLength = 60;
            txt_regrId.Name = "txt_regrId";
            txt_regrId.Size = new Size(100, 23);
            txt_regrId.TabIndex = 43;
            txt_regrId.Text = "1";
            // 
            // txt_modrNm
            // 
            txt_modrNm.Location = new Point(287, 200);
            txt_modrNm.MaxLength = 60;
            txt_modrNm.Name = "txt_modrNm";
            txt_modrNm.Size = new Size(100, 23);
            txt_modrNm.TabIndex = 44;
            txt_modrNm.Text = "Jacq";
            // 
            // cmb_vatCatCd
            // 
            cmb_vatCatCd.FormattingEnabled = true;
            cmb_vatCatCd.Location = new Point(287, 28);
            cmb_vatCatCd.Name = "cmb_vatCatCd";
            cmb_vatCatCd.Size = new Size(100, 23);
            cmb_vatCatCd.TabIndex = 61;
            // 
            // lbl_rrp
            // 
            lbl_rrp.AutoSize = true;
            lbl_rrp.Location = new Point(22, 245);
            lbl_rrp.Name = "lbl_rrp";
            lbl_rrp.Size = new Size(305, 15);
            lbl_rrp.TabIndex = 17;
            lbl_rrp.Text = "Recommended Retailed Price as set by the manufacturer";
            // 
            // txt_itemStdNm
            // 
            txt_itemStdNm.Location = new Point(395, 30);
            txt_itemStdNm.MaxLength = 200;
            txt_itemStdNm.Name = "txt_itemStdNm";
            txt_itemStdNm.Size = new Size(100, 23);
            txt_itemStdNm.TabIndex = 46;
            // 
            // lbl_svcChargeYn
            // 
            lbl_svcChargeYn.AutoSize = true;
            lbl_svcChargeYn.Location = new Point(22, 274);
            lbl_svcChargeYn.Name = "lbl_svcChargeYn";
            lbl_svcChargeYn.Size = new Size(343, 15);
            lbl_svcChargeYn.TabIndex = 18;
            lbl_svcChargeYn.Text = "Indication of whether item being registered has a service charge";
            // 
            // txt_btchNo
            // 
            txt_btchNo.Location = new Point(395, 61);
            txt_btchNo.MaxLength = 10;
            txt_btchNo.Name = "txt_btchNo";
            txt_btchNo.ShortcutsEnabled = false;
            txt_btchNo.Size = new Size(100, 23);
            txt_btchNo.TabIndex = 47;
            // 
            // lbl_manufacturerItemCd
            // 
            lbl_manufacturerItemCd.AutoSize = true;
            lbl_manufacturerItemCd.Location = new Point(22, 216);
            lbl_manufacturerItemCd.Name = "lbl_manufacturerItemCd";
            lbl_manufacturerItemCd.Size = new Size(226, 15);
            lbl_manufacturerItemCd.TabIndex = 15;
            lbl_manufacturerItemCd.Text = "Manufacturer item code for MTV product";
            // 
            // txt_bcd
            // 
            txt_bcd.Location = new Point(395, 90);
            txt_bcd.MaxLength = 20;
            txt_bcd.Name = "txt_bcd";
            txt_bcd.Size = new Size(100, 23);
            txt_bcd.TabIndex = 48;
            // 
            // lbl_rentalYn
            // 
            lbl_rentalYn.AutoSize = true;
            lbl_rentalYn.Location = new Point(22, 306);
            lbl_rentalYn.Name = "lbl_rentalYn";
            lbl_rentalYn.Size = new Size(328, 15);
            lbl_rentalYn.TabIndex = 19;
            lbl_rentalYn.Text = "Indication of whether item being registered has rental charge";
            // 
            // txt_dftPrc
            // 
            txt_dftPrc.Location = new Point(395, 117);
            txt_dftPrc.Name = "txt_dftPrc";
            txt_dftPrc.Size = new Size(100, 23);
            txt_dftPrc.TabIndex = 49;
            txt_dftPrc.TextChanged += txt_dftPrc_TextChanged;
            txt_dftPrc.KeyPress += txt_dftPrc_KeyPress;
            txt_dftPrc.Validating += txt_dftPrc_Validating;
            // 
            // lbl_manufactuterTpin
            // 
            lbl_manufactuterTpin.AutoSize = true;
            lbl_manufactuterTpin.Location = new Point(22, 187);
            lbl_manufactuterTpin.Name = "lbl_manufactuterTpin";
            lbl_manufactuterTpin.Size = new Size(199, 15);
            lbl_manufactuterTpin.TabIndex = 14;
            lbl_manufactuterTpin.Text = "Manufacturer TPIN for MTV product";
            // 
            // lbl_sftyQty
            // 
            lbl_sftyQty.AutoSize = true;
            lbl_sftyQty.Location = new Point(22, 156);
            lbl_sftyQty.Name = "lbl_sftyQty";
            lbl_sftyQty.Size = new Size(354, 15);
            lbl_sftyQty.TabIndex = 13;
            lbl_sftyQty.Text = "Additional inventory or stock that a business maintains as a buffer";
            // 
            // lbl_addInfo
            // 
            lbl_addInfo.AutoSize = true;
            lbl_addInfo.Location = new Point(22, 343);
            lbl_addInfo.Name = "lbl_addInfo";
            lbl_addInfo.Size = new Size(231, 15);
            lbl_addInfo.TabIndex = 12;
            lbl_addInfo.Text = "Any additional information about the item";
            // 
            // txt_manufactuterTpin
            // 
            txt_manufactuterTpin.Location = new Point(395, 179);
            txt_manufactuterTpin.MaxLength = 10;
            txt_manufactuterTpin.Name = "txt_manufactuterTpin";
            txt_manufactuterTpin.Size = new Size(100, 23);
            txt_manufactuterTpin.TabIndex = 52;
            // 
            // lbl_dftPrc
            // 
            lbl_dftPrc.AutoSize = true;
            lbl_dftPrc.Location = new Point(22, 125);
            lbl_dftPrc.Name = "lbl_dftPrc";
            lbl_dftPrc.Size = new Size(153, 15);
            lbl_dftPrc.TabIndex = 11;
            lbl_dftPrc.Text = "This is the default unit price";
            // 
            // txt_manufacturerItemCd
            // 
            txt_manufacturerItemCd.Location = new Point(395, 208);
            txt_manufacturerItemCd.MaxLength = 10;
            txt_manufacturerItemCd.Name = "txt_manufacturerItemCd";
            txt_manufacturerItemCd.Size = new Size(100, 23);
            txt_manufacturerItemCd.TabIndex = 53;
            // 
            // lbl_bcd
            // 
            lbl_bcd.AutoSize = true;
            lbl_bcd.Location = new Point(22, 93);
            lbl_bcd.Name = "lbl_bcd";
            lbl_bcd.Size = new Size(133, 15);
            lbl_bcd.TabIndex = 21;
            lbl_bcd.Text = "This is the item barcode";
            // 
            // txt_rrp
            // 
            txt_rrp.Location = new Point(395, 237);
            txt_rrp.Name = "txt_rrp";
            txt_rrp.Size = new Size(100, 23);
            txt_rrp.TabIndex = 54;
            txt_rrp.TextChanged += txt_rrp_TextChanged;
            txt_rrp.KeyPress += txt_rrp_KeyPress;
            txt_rrp.Validating += txt_rrp_Validating;
            // 
            // lbl_btchNo
            // 
            lbl_btchNo.AutoSize = true;
            lbl_btchNo.Location = new Point(22, 69);
            lbl_btchNo.Name = "lbl_btchNo";
            lbl_btchNo.Size = new Size(165, 15);
            lbl_btchNo.TabIndex = 25;
            lbl_btchNo.Text = "This is the item batch number";
            // 
            // lbl_itemStdNm
            // 
            lbl_itemStdNm.AutoSize = true;
            lbl_itemStdNm.Location = new Point(22, 38);
            lbl_itemStdNm.Name = "lbl_itemStdNm";
            lbl_itemStdNm.Size = new Size(116, 15);
            lbl_itemStdNm.TabIndex = 30;
            lbl_itemStdNm.Text = "Item Standard Name";
            // 
            // rtb_addInfo
            // 
            rtb_addInfo.Location = new Point(395, 343);
            rtb_addInfo.MaxLength = 100;
            rtb_addInfo.Name = "rtb_addInfo";
            rtb_addInfo.Size = new Size(150, 96);
            rtb_addInfo.TabIndex = 57;
            rtb_addInfo.Text = "";
            // 
            // numericUpDown1
            // 
            numericUpDown1.Location = new Point(395, 146);
            numericUpDown1.Name = "numericUpDown1";
            numericUpDown1.Size = new Size(100, 23);
            numericUpDown1.TabIndex = 58;
            // 
            // grp_Optional
            // 
            grp_Optional.Controls.Add(cmb_svcChargeYn);
            grp_Optional.Controls.Add(cmb_rentalYn);
            grp_Optional.Controls.Add(numericUpDown1);
            grp_Optional.Controls.Add(rtb_addInfo);
            grp_Optional.Controls.Add(lbl_itemStdNm);
            grp_Optional.Controls.Add(lbl_btchNo);
            grp_Optional.Controls.Add(txt_rrp);
            grp_Optional.Controls.Add(lbl_bcd);
            grp_Optional.Controls.Add(txt_manufacturerItemCd);
            grp_Optional.Controls.Add(lbl_dftPrc);
            grp_Optional.Controls.Add(txt_manufactuterTpin);
            grp_Optional.Controls.Add(lbl_addInfo);
            grp_Optional.Controls.Add(lbl_sftyQty);
            grp_Optional.Controls.Add(lbl_manufactuterTpin);
            grp_Optional.Controls.Add(txt_dftPrc);
            grp_Optional.Controls.Add(lbl_rentalYn);
            grp_Optional.Controls.Add(txt_bcd);
            grp_Optional.Controls.Add(lbl_manufacturerItemCd);
            grp_Optional.Controls.Add(txt_btchNo);
            grp_Optional.Controls.Add(lbl_svcChargeYn);
            grp_Optional.Controls.Add(txt_itemStdNm);
            grp_Optional.Controls.Add(lbl_rrp);
            grp_Optional.Location = new Point(463, 12);
            grp_Optional.Name = "grp_Optional";
            grp_Optional.Size = new Size(560, 466);
            grp_Optional.TabIndex = 31;
            grp_Optional.TabStop = false;
            grp_Optional.Text = "Optional";
            grp_Optional.Enter += grp_Optional_Enter;
            // 
            // cmb_svcChargeYn
            // 
            cmb_svcChargeYn.FormattingEnabled = true;
            cmb_svcChargeYn.Location = new Point(395, 266);
            cmb_svcChargeYn.Name = "cmb_svcChargeYn";
            cmb_svcChargeYn.Size = new Size(100, 23);
            cmb_svcChargeYn.TabIndex = 60;
            // 
            // cmb_rentalYn
            // 
            cmb_rentalYn.FormattingEnabled = true;
            cmb_rentalYn.Items.AddRange(new object[] { "N", "Y" });
            cmb_rentalYn.Location = new Point(395, 298);
            cmb_rentalYn.Name = "cmb_rentalYn";
            cmb_rentalYn.Size = new Size(100, 23);
            cmb_rentalYn.TabIndex = 59;
            cmb_rentalYn.SelectedIndexChanged += cmb_rentalYn_SelectedIndexChanged;
            // 
            // toolTip_USNPSC
            // 
            toolTip_USNPSC.Popup += toolTip_USNPSC_Popup;
            // 
            // grp_PossibleNull
            // 
            grp_PossibleNull.Controls.Add(cmb_exciseTxCatCd);
            grp_PossibleNull.Controls.Add(cmb_tlCatCd);
            grp_PossibleNull.Controls.Add(cmb_iplCatCd);
            grp_PossibleNull.Controls.Add(cmb_vatCatCd);
            grp_PossibleNull.Controls.Add(lbl_iplCatCd);
            grp_PossibleNull.Controls.Add(lbl_vatCatCd);
            grp_PossibleNull.Controls.Add(lbl_tlCatCd);
            grp_PossibleNull.Controls.Add(lbl_exciseTxCatCd);
            grp_PossibleNull.Location = new Point(24, 496);
            grp_PossibleNull.Name = "grp_PossibleNull";
            grp_PossibleNull.Size = new Size(422, 174);
            grp_PossibleNull.TabIndex = 61;
            grp_PossibleNull.TabStop = false;
            grp_PossibleNull.Text = "Where not applicable, pass null";
            // 
            // cmb_exciseTxCatCd
            // 
            cmb_exciseTxCatCd.FormattingEnabled = true;
            cmb_exciseTxCatCd.Location = new Point(286, 120);
            cmb_exciseTxCatCd.Name = "cmb_exciseTxCatCd";
            cmb_exciseTxCatCd.Size = new Size(100, 23);
            cmb_exciseTxCatCd.TabIndex = 62;
            // 
            // cmb_tlCatCd
            // 
            cmb_tlCatCd.FormattingEnabled = true;
            cmb_tlCatCd.Location = new Point(286, 91);
            cmb_tlCatCd.Name = "cmb_tlCatCd";
            cmb_tlCatCd.Size = new Size(100, 23);
            cmb_tlCatCd.TabIndex = 62;
            // 
            // cmb_iplCatCd
            // 
            cmb_iplCatCd.FormattingEnabled = true;
            cmb_iplCatCd.Location = new Point(286, 57);
            cmb_iplCatCd.Name = "cmb_iplCatCd";
            cmb_iplCatCd.Size = new Size(101, 23);
            cmb_iplCatCd.TabIndex = 62;
            // 
            // btn_AddItem
            // 
            btn_AddItem.Location = new Point(463, 496);
            btn_AddItem.Name = "btn_AddItem";
            btn_AddItem.Size = new Size(75, 23);
            btn_AddItem.TabIndex = 62;
            btn_AddItem.Text = "Add Item";
            btn_AddItem.UseVisualStyleBackColor = true;
            btn_AddItem.Click += btn_AddItem_Click;
            // 
            // frm_AddItem
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1040, 698);
            Controls.Add(btn_AddItem);
            Controls.Add(grp_PossibleNull);
            Controls.Add(grp_Optional);
            Controls.Add(grp_Required);
            Name = "frm_AddItem";
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.CenterParent;
            Text = "Add Item";
            Load += frm_AddItem_Load;
            grp_Required.ResumeLayout(false);
            grp_Required.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown1).EndInit();
            grp_Optional.ResumeLayout(false);
            grp_Optional.PerformLayout();
            grp_PossibleNull.ResumeLayout(false);
            grp_PossibleNull.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private Label lbl_Tpin;
        private Label lbl_bhfId;
        private Label lbl_itemClsCd;
        private Label lbl_itemTyCd;
        private Label lbl_qtyUnitCd;
        private Label lbl_pkgUnitCd;
        private Label lbl_orgnNatCd;
        private Label lbl_itemNm;
        private Label lbl_iplCatCd;
        private Label lbl_vatCatCd;
        private Label lbl_regrNm;
        private Label lbl_useYn;
        private Label lbl_modrNm;
        private Label lbl_regrId;
        private Label lbl_modrId;
        private Label lbl_exciseTxCatCd;
        private Label lbl_tlCatCd;
        private GroupBox grp_Required;
        private TextBox txt_itemClsCd;
        private TextBox txt_bhfId;
        private TextBox txt_Tpin;
        private TextBox txt_regrId;
        private TextBox txt_regrNm;
        private TextBox txt_itemNm;
        private TextBox txt_modrId;
        private TextBox txt_modrNm;
        private TextBox txt_itemCd;
        private Label lbl_itemCd;
        private Label lbl_rrp;
        private TextBox txt_itemStdNm;
        private Label lbl_svcChargeYn;
        private TextBox txt_btchNo;
        private Label lbl_manufacturerItemCd;
        private TextBox txt_bcd;
        private Label lbl_rentalYn;
        private TextBox txt_dftPrc;
        private Label lbl_manufactuterTpin;
        private Label lbl_sftyQty;
        private Label lbl_addInfo;
        private TextBox txt_manufactuterTpin;
        private Label lbl_dftPrc;
        private TextBox txt_manufacturerItemCd;
        private Label lbl_bcd;
        private TextBox txt_rrp;
        private Label lbl_btchNo;
        private Label lbl_itemStdNm;
        private RichTextBox rtb_addInfo;
        private NumericUpDown numericUpDown1;
        private GroupBox grp_Optional;
        private ComboBox cmb_rentalYn;
        private ComboBox cmb_svcChargeYn;
        private ComboBox cmb_useYn;
        private LinkLabel llb_itemClsCd;
        private ToolTip toolTip_USNPSC;
        private ComboBox cmb_itemTyCd;
        private ComboBox cmb_orgnNatCd;
        private ComboBox cmb_pkgUnitCd;
        private ComboBox cmb_qtyUnitCd;
        private ComboBox cmb_vatCatCd;
        private GroupBox grp_PossibleNull;
        private ComboBox cmb_iplCatCd;
        private ComboBox cmb_tlCatCd;
        private ComboBox cmb_exciseTxCatCd;
        private Button btn_AddItem;
    }
}
