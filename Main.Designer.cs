﻿namespace Zambia.Invoice
{
    partial class frmMain
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmMain));
            btn_ReloadCodes = new Button();
            btn_ReloadClasses = new Button();
            btn_Items = new Button();
            btnVIewCodes = new Button();
            btnViewClass = new Button();
            btnInvoice = new Button();
            btn_LoadPendingInvoices = new Button();
            grb_advancedFunctions = new GroupBox();
            dgv_pendingInvoices = new DataGridView();
            col_InvoiceNum = new DataGridViewTextBoxColumn();
            col_CustomerId = new DataGridViewTextBoxColumn();
            col_InvoiceDate = new DataGridViewTextBoxColumn();
            col_ItemCount = new DataGridViewTextBoxColumn();
            col_InvoiceTotal = new DataGridViewTextBoxColumn();
            col_TaxTotal = new DataGridViewTextBoxColumn();
            col_Process = new DataGridViewButtonColumn();
            grb_proccessInv = new GroupBox();
            prb_invoiceStatus = new ProgressBar();
            btn_process = new Button();
            ZRA_tabs = new TabControl();
            tab_pending = new TabPage();
            tab_completed = new TabPage();
            grb_Invoices = new GroupBox();
            btn_RefreshCompleted = new Button();
            dgv_CompletedInvoices = new DataGridView();
            col_SubmitDate = new DataGridViewTextBoxColumn();
            col_InvoiceId = new DataGridViewTextBoxColumn();
            col_MerchantNo = new DataGridViewTextBoxColumn();
            col_CIS = new DataGridViewTextBoxColumn();
            col_URL = new DataGridViewLinkColumn();
            col_ViewQR = new DataGridViewButtonColumn();
            tab_advanced = new TabPage();
            tab_about = new TabPage();
            lbl_icons = new Label();
            lbl_copy = new Label();
            grb_advancedFunctions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgv_pendingInvoices).BeginInit();
            grb_proccessInv.SuspendLayout();
            ZRA_tabs.SuspendLayout();
            tab_pending.SuspendLayout();
            tab_completed.SuspendLayout();
            grb_Invoices.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgv_CompletedInvoices).BeginInit();
            tab_advanced.SuspendLayout();
            tab_about.SuspendLayout();
            SuspendLayout();
            // 
            // btn_ReloadCodes
            // 
            btn_ReloadCodes.Location = new Point(22, 37);
            btn_ReloadCodes.Name = "btn_ReloadCodes";
            btn_ReloadCodes.Size = new Size(126, 23);
            btn_ReloadCodes.TabIndex = 0;
            btn_ReloadCodes.Text = "Reload Codes";
            btn_ReloadCodes.UseVisualStyleBackColor = true;
            btn_ReloadCodes.Click += btn_ReloadCodes_Click;
            // 
            // btn_ReloadClasses
            // 
            btn_ReloadClasses.Location = new Point(22, 66);
            btn_ReloadClasses.Name = "btn_ReloadClasses";
            btn_ReloadClasses.Size = new Size(126, 23);
            btn_ReloadClasses.TabIndex = 1;
            btn_ReloadClasses.Text = "Reload Classes";
            btn_ReloadClasses.UseVisualStyleBackColor = true;
            btn_ReloadClasses.Click += btn_ReloadClasses_Click;
            // 
            // btn_Items
            // 
            btn_Items.Location = new Point(22, 95);
            btn_Items.Name = "btn_Items";
            btn_Items.Size = new Size(126, 23);
            btn_Items.TabIndex = 2;
            btn_Items.Text = "Items";
            btn_Items.UseVisualStyleBackColor = true;
            btn_Items.Click += btn_Items_Click;
            // 
            // btnVIewCodes
            // 
            btnVIewCodes.Location = new Point(165, 37);
            btnVIewCodes.Name = "btnVIewCodes";
            btnVIewCodes.Size = new Size(119, 23);
            btnVIewCodes.TabIndex = 4;
            btnVIewCodes.Text = "Vew Codes";
            btnVIewCodes.UseVisualStyleBackColor = true;
            btnVIewCodes.Click += btnVIewCodes_Click;
            // 
            // btnViewClass
            // 
            btnViewClass.Location = new Point(165, 66);
            btnViewClass.Name = "btnViewClass";
            btnViewClass.Size = new Size(119, 23);
            btnViewClass.TabIndex = 5;
            btnViewClass.Text = "View Classes";
            btnViewClass.UseVisualStyleBackColor = true;
            btnViewClass.Click += btnViewClass_Click;
            // 
            // btnInvoice
            // 
            btnInvoice.Location = new Point(165, 95);
            btnInvoice.Name = "btnInvoice";
            btnInvoice.Size = new Size(119, 23);
            btnInvoice.TabIndex = 6;
            btnInvoice.Text = "Create Invoice";
            btnInvoice.UseVisualStyleBackColor = true;
            btnInvoice.Click += btnInvoice_Click;
            // 
            // btn_LoadPendingInvoices
            // 
            btn_LoadPendingInvoices.Image = (Image)resources.GetObject("btn_LoadPendingInvoices.Image");
            btn_LoadPendingInvoices.ImageAlign = ContentAlignment.MiddleRight;
            btn_LoadPendingInvoices.Location = new Point(6, 22);
            btn_LoadPendingInvoices.Name = "btn_LoadPendingInvoices";
            btn_LoadPendingInvoices.Size = new Size(127, 23);
            btn_LoadPendingInvoices.TabIndex = 8;
            btn_LoadPendingInvoices.Text = "Refresh";
            btn_LoadPendingInvoices.UseVisualStyleBackColor = true;
            btn_LoadPendingInvoices.Click += btn_LoadPendingInvoices_Click;
            // 
            // grb_advancedFunctions
            // 
            grb_advancedFunctions.Controls.Add(btn_ReloadCodes);
            grb_advancedFunctions.Controls.Add(btn_ReloadClasses);
            grb_advancedFunctions.Controls.Add(btn_Items);
            grb_advancedFunctions.Controls.Add(btnInvoice);
            grb_advancedFunctions.Controls.Add(btnVIewCodes);
            grb_advancedFunctions.Controls.Add(btnViewClass);
            grb_advancedFunctions.Location = new Point(6, 6);
            grb_advancedFunctions.Name = "grb_advancedFunctions";
            grb_advancedFunctions.Size = new Size(548, 140);
            grb_advancedFunctions.TabIndex = 9;
            grb_advancedFunctions.TabStop = false;
            grb_advancedFunctions.Text = "ZRA Functions (Advanced)";
            // 
            // dgv_pendingInvoices
            // 
            dgv_pendingInvoices.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv_pendingInvoices.Columns.AddRange(new DataGridViewColumn[] { col_InvoiceNum, col_CustomerId, col_InvoiceDate, col_ItemCount, col_InvoiceTotal, col_TaxTotal, col_Process });
            dgv_pendingInvoices.Location = new Point(6, 52);
            dgv_pendingInvoices.Name = "dgv_pendingInvoices";
            dgv_pendingInvoices.Size = new Size(658, 453);
            dgv_pendingInvoices.TabIndex = 10;
            // 
            // col_InvoiceNum
            // 
            col_InvoiceNum.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            col_InvoiceNum.HeaderText = "Invoice Number";
            col_InvoiceNum.Name = "col_InvoiceNum";
            col_InvoiceNum.Width = 107;
            // 
            // col_CustomerId
            // 
            col_CustomerId.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            col_CustomerId.HeaderText = "Customer ID";
            col_CustomerId.Name = "col_CustomerId";
            col_CustomerId.Width = 90;
            // 
            // col_InvoiceDate
            // 
            col_InvoiceDate.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            col_InvoiceDate.HeaderText = "Invoice Date";
            col_InvoiceDate.Name = "col_InvoiceDate";
            col_InvoiceDate.Width = 89;
            // 
            // col_ItemCount
            // 
            col_ItemCount.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            col_ItemCount.HeaderText = "Invoice Items";
            col_ItemCount.Name = "col_ItemCount";
            col_ItemCount.Width = 94;
            // 
            // col_InvoiceTotal
            // 
            col_InvoiceTotal.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            col_InvoiceTotal.HeaderText = "Invoice Total";
            col_InvoiceTotal.Name = "col_InvoiceTotal";
            col_InvoiceTotal.Width = 91;
            // 
            // col_TaxTotal
            // 
            col_TaxTotal.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            col_TaxTotal.HeaderText = "Total Tax";
            col_TaxTotal.Name = "col_TaxTotal";
            col_TaxTotal.Width = 72;
            // 
            // col_Process
            // 
            col_Process.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            col_Process.HeaderText = "Process";
            col_Process.Name = "col_Process";
            col_Process.Width = 53;
            // 
            // grb_proccessInv
            // 
            grb_proccessInv.Controls.Add(prb_invoiceStatus);
            grb_proccessInv.Controls.Add(btn_process);
            grb_proccessInv.Controls.Add(dgv_pendingInvoices);
            grb_proccessInv.Controls.Add(btn_LoadPendingInvoices);
            grb_proccessInv.Location = new Point(6, 6);
            grb_proccessInv.Name = "grb_proccessInv";
            grb_proccessInv.Size = new Size(684, 512);
            grb_proccessInv.TabIndex = 11;
            grb_proccessInv.TabStop = false;
            grb_proccessInv.Text = "Invoices pending";
            // 
            // prb_invoiceStatus
            // 
            prb_invoiceStatus.Location = new Point(272, 22);
            prb_invoiceStatus.Name = "prb_invoiceStatus";
            prb_invoiceStatus.Size = new Size(278, 23);
            prb_invoiceStatus.TabIndex = 12;
            prb_invoiceStatus.Visible = false;
            // 
            // btn_process
            // 
            btn_process.Image = (Image)resources.GetObject("btn_process.Image");
            btn_process.ImageAlign = ContentAlignment.MiddleRight;
            btn_process.Location = new Point(139, 22);
            btn_process.Name = "btn_process";
            btn_process.Size = new Size(127, 23);
            btn_process.TabIndex = 11;
            btn_process.Text = "Process All";
            btn_process.UseVisualStyleBackColor = true;
            btn_process.Click += btn_process_Click;
            // 
            // ZRA_tabs
            // 
            ZRA_tabs.Controls.Add(tab_pending);
            ZRA_tabs.Controls.Add(tab_completed);
            ZRA_tabs.Controls.Add(tab_advanced);
            ZRA_tabs.Controls.Add(tab_about);
            ZRA_tabs.Location = new Point(12, 12);
            ZRA_tabs.Name = "ZRA_tabs";
            ZRA_tabs.SelectedIndex = 0;
            ZRA_tabs.Size = new Size(704, 552);
            ZRA_tabs.TabIndex = 13;
            // 
            // tab_pending
            // 
            tab_pending.Controls.Add(grb_proccessInv);
            tab_pending.Location = new Point(4, 24);
            tab_pending.Name = "tab_pending";
            tab_pending.Padding = new Padding(3);
            tab_pending.Size = new Size(696, 524);
            tab_pending.TabIndex = 0;
            tab_pending.Text = "Pending Invoices";
            tab_pending.UseVisualStyleBackColor = true;
            // 
            // tab_completed
            // 
            tab_completed.Controls.Add(grb_Invoices);
            tab_completed.Location = new Point(4, 24);
            tab_completed.Name = "tab_completed";
            tab_completed.Padding = new Padding(3);
            tab_completed.Size = new Size(696, 524);
            tab_completed.TabIndex = 1;
            tab_completed.Text = "Completd Invoices";
            tab_completed.UseVisualStyleBackColor = true;
            // 
            // grb_Invoices
            // 
            grb_Invoices.Controls.Add(btn_RefreshCompleted);
            grb_Invoices.Controls.Add(dgv_CompletedInvoices);
            grb_Invoices.Location = new Point(6, 6);
            grb_Invoices.Name = "grb_Invoices";
            grb_Invoices.Size = new Size(684, 512);
            grb_Invoices.TabIndex = 16;
            grb_Invoices.TabStop = false;
            grb_Invoices.Text = "Invoices";
            // 
            // btn_RefreshCompleted
            // 
            btn_RefreshCompleted.Image = (Image)resources.GetObject("btn_RefreshCompleted.Image");
            btn_RefreshCompleted.ImageAlign = ContentAlignment.MiddleRight;
            btn_RefreshCompleted.Location = new Point(6, 22);
            btn_RefreshCompleted.Name = "btn_RefreshCompleted";
            btn_RefreshCompleted.Size = new Size(127, 23);
            btn_RefreshCompleted.TabIndex = 9;
            btn_RefreshCompleted.Text = "Refresh";
            btn_RefreshCompleted.UseVisualStyleBackColor = true;
            btn_RefreshCompleted.Click += btn_RefreshCompleted_Click;
            // 
            // dgv_CompletedInvoices
            // 
            dgv_CompletedInvoices.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv_CompletedInvoices.Columns.AddRange(new DataGridViewColumn[] { col_SubmitDate, col_InvoiceId, col_MerchantNo, col_CIS, col_URL, col_ViewQR });
            dgv_CompletedInvoices.Location = new Point(6, 57);
            dgv_CompletedInvoices.Name = "dgv_CompletedInvoices";
            dgv_CompletedInvoices.Size = new Size(672, 449);
            dgv_CompletedInvoices.TabIndex = 0;
            // 
            // col_SubmitDate
            // 
            col_SubmitDate.HeaderText = "Submit Date";
            col_SubmitDate.Name = "col_SubmitDate";
            // 
            // col_InvoiceId
            // 
            col_InvoiceId.HeaderText = "Invoice #";
            col_InvoiceId.Name = "col_InvoiceId";
            // 
            // col_MerchantNo
            // 
            col_MerchantNo.HeaderText = "Merchant #";
            col_MerchantNo.Name = "col_MerchantNo";
            // 
            // col_CIS
            // 
            col_CIS.HeaderText = "CIS";
            col_CIS.Name = "col_CIS";
            // 
            // col_URL
            // 
            col_URL.HeaderText = "View Invoice";
            col_URL.Name = "col_URL";
            // 
            // col_ViewQR
            // 
            col_ViewQR.HeaderText = "QR Code";
            col_ViewQR.Name = "col_ViewQR";
            col_ViewQR.Resizable = DataGridViewTriState.True;
            col_ViewQR.SortMode = DataGridViewColumnSortMode.Automatic;
            // 
            // tab_advanced
            // 
            tab_advanced.Controls.Add(grb_advancedFunctions);
            tab_advanced.Location = new Point(4, 24);
            tab_advanced.Name = "tab_advanced";
            tab_advanced.Padding = new Padding(3);
            tab_advanced.Size = new Size(696, 524);
            tab_advanced.TabIndex = 2;
            tab_advanced.Text = "Advanced";
            tab_advanced.UseVisualStyleBackColor = true;
            // 
            // tab_about
            // 
            tab_about.Controls.Add(lbl_icons);
            tab_about.Location = new Point(4, 24);
            tab_about.Name = "tab_about";
            tab_about.Padding = new Padding(3);
            tab_about.Size = new Size(696, 524);
            tab_about.TabIndex = 3;
            tab_about.Text = "About";
            tab_about.UseVisualStyleBackColor = true;
            // 
            // lbl_icons
            // 
            lbl_icons.AutoSize = true;
            lbl_icons.Font = new Font("Microsoft Yi Baiti", 7F);
            lbl_icons.Location = new Point(6, 510);
            lbl_icons.Name = "lbl_icons";
            lbl_icons.Size = new Size(62, 10);
            lbl_icons.TabIndex = 0;
            lbl_icons.Text = "Icons by Icons8";
            // 
            // lbl_copy
            // 
            lbl_copy.AutoSize = true;
            lbl_copy.Location = new Point(491, 567);
            lbl_copy.Name = "lbl_copy";
            lbl_copy.Size = new Size(199, 15);
            lbl_copy.TabIndex = 14;
            lbl_copy.Text = "Copyright © 2023 Primedia Outdoor";
            // 
            // frmMain
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(720, 587);
            Controls.Add(lbl_copy);
            Controls.Add(ZRA_tabs);
            Name = "frmMain";
            Text = "ZRA Smart Invoice";
            Load += frmMain_Load;
            grb_advancedFunctions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgv_pendingInvoices).EndInit();
            grb_proccessInv.ResumeLayout(false);
            ZRA_tabs.ResumeLayout(false);
            tab_pending.ResumeLayout(false);
            tab_completed.ResumeLayout(false);
            grb_Invoices.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgv_CompletedInvoices).EndInit();
            tab_advanced.ResumeLayout(false);
            tab_about.ResumeLayout(false);
            tab_about.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Button btn_ReloadCodes;
        private Button btn_ReloadClasses;
        private Button btn_Items;
        private Button btnVIewCodes;
        private Button btnViewClass;
        private Button btnInvoice;
        private Button btn_LoadPendingInvoices;
        private GroupBox grb_advancedFunctions;
        private DataGridView dgv_pendingInvoices;
        private GroupBox grb_proccessInv;
        private TabControl ZRA_tabs;
        private TabPage tab_pending;
        private TabPage tab_completed;
        private TabPage tab_advanced;
        private TabPage tab_about;
        private Label lbl_icons;
        private Label lbl_copy;
        private Button btn_process;
        private ProgressBar prb_invoiceStatus;
        private DataGridViewTextBoxColumn col_InvoiceNum;
        private DataGridViewTextBoxColumn col_CustomerId;
        private DataGridViewTextBoxColumn col_InvoiceDate;
        private DataGridViewTextBoxColumn col_ItemCount;
        private DataGridViewTextBoxColumn col_InvoiceTotal;
        private DataGridViewTextBoxColumn col_TaxTotal;
        private DataGridViewButtonColumn col_Process;
        private GroupBox grb_Invoices;
        private DataGridView dgv_CompletedInvoices;
        private Button btn_RefreshCompleted;
        private DataGridViewTextBoxColumn col_SubmitDate;
        private DataGridViewTextBoxColumn col_InvoiceId;
        private DataGridViewTextBoxColumn col_MerchantNo;
        private DataGridViewTextBoxColumn col_CIS;
        private DataGridViewLinkColumn col_URL;
        private DataGridViewButtonColumn col_ViewQR;
    }
}
