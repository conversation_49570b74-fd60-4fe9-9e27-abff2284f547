﻿namespace Zambia.Invoice.Forms.InvoiceForms
{
    partial class frmInvoice
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            grbRequired = new GroupBox();
            cmb_currencyTyCd = new ComboBox();
            cmb_prchrAcptcYn = new ComboBox();
            label39 = new Label();
            cmb_pmtTyCd = new ComboBox();
            dtp_salesDt = new DateTimePicker();
            cmb_salesSttsCd = new ComboBox();
            cmb_rcptTyCd = new ComboBox();
            cmb_salesTyCd = new ComboBox();
            dtp_cfmDt = new DateTimePicker();
            txt_regrNm = new TextBox();
            label33 = new Label();
            txt_exchangeRt = new TextBox();
            label13 = new Label();
            label14 = new Label();
            label15 = new Label();
            txt_regrId = new TextBox();
            label10 = new Label();
            txt_totAmt = new TextBox();
            txt_totTaxAmt = new TextBox();
            label11 = new Label();
            label12 = new Label();
            txt_totTaxblAmt = new TextBox();
            txt_totItemCnt = new TextBox();
            label5 = new Label();
            label6 = new Label();
            label7 = new Label();
            label8 = new Label();
            label3 = new Label();
            label4 = new Label();
            txt_bhfId = new TextBox();
            txt_tpin = new TextBox();
            lbl_bhfId = new Label();
            lbl_tpin = new Label();
            txt_cisInvcNo = new TextBox();
            label31 = new Label();
            label28 = new Label();
            txt_modrNm = new TextBox();
            label16 = new Label();
            txt_modrId = new TextBox();
            label9 = new Label();
            lbl_ItemTitle = new Label();
            dgv_Items = new DataGridView();
            col_sequence = new DataGridViewTextBoxColumn();
            col_itemCode = new DataGridViewTextBoxColumn();
            col_classification_code = new DataGridViewTextBoxColumn();
            col_itemName = new DataGridViewTextBoxColumn();
            col_packagingCode = new DataGridViewTextBoxColumn();
            col_packageQuantity = new DataGridViewTextBoxColumn();
            col_quantityUnitCode = new DataGridViewTextBoxColumn();
            col_quantity = new DataGridViewTextBoxColumn();
            col_unitPrice = new DataGridViewTextBoxColumn();
            col_supplyAmount = new DataGridViewTextBoxColumn();
            col_VATcode = new DataGridViewTextBoxColumn();
            col_totalAmount = new DataGridViewTextBoxColumn();
            btn_AddItem = new Button();
            btn_CreateInvoice = new Button();
            grbRequired.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgv_Items).BeginInit();
            SuspendLayout();
            // 
            // grbRequired
            // 
            grbRequired.Controls.Add(cmb_currencyTyCd);
            grbRequired.Controls.Add(cmb_prchrAcptcYn);
            grbRequired.Controls.Add(label39);
            grbRequired.Controls.Add(cmb_pmtTyCd);
            grbRequired.Controls.Add(dtp_salesDt);
            grbRequired.Controls.Add(cmb_salesSttsCd);
            grbRequired.Controls.Add(cmb_rcptTyCd);
            grbRequired.Controls.Add(cmb_salesTyCd);
            grbRequired.Controls.Add(dtp_cfmDt);
            grbRequired.Controls.Add(txt_regrNm);
            grbRequired.Controls.Add(label33);
            grbRequired.Controls.Add(txt_exchangeRt);
            grbRequired.Controls.Add(label13);
            grbRequired.Controls.Add(label14);
            grbRequired.Controls.Add(label15);
            grbRequired.Controls.Add(txt_regrId);
            grbRequired.Controls.Add(label10);
            grbRequired.Controls.Add(txt_totAmt);
            grbRequired.Controls.Add(txt_totTaxAmt);
            grbRequired.Controls.Add(label11);
            grbRequired.Controls.Add(label12);
            grbRequired.Controls.Add(txt_totTaxblAmt);
            grbRequired.Controls.Add(txt_totItemCnt);
            grbRequired.Controls.Add(label5);
            grbRequired.Controls.Add(label6);
            grbRequired.Controls.Add(label7);
            grbRequired.Controls.Add(label8);
            grbRequired.Controls.Add(label3);
            grbRequired.Controls.Add(label4);
            grbRequired.Controls.Add(txt_bhfId);
            grbRequired.Controls.Add(txt_tpin);
            grbRequired.Controls.Add(lbl_bhfId);
            grbRequired.Controls.Add(lbl_tpin);
            grbRequired.Controls.Add(txt_cisInvcNo);
            grbRequired.Controls.Add(label31);
            grbRequired.Controls.Add(label28);
            grbRequired.Location = new Point(16, 16);
            grbRequired.Name = "grbRequired";
            grbRequired.Size = new Size(767, 375);
            grbRequired.TabIndex = 0;
            grbRequired.TabStop = false;
            grbRequired.Text = "Required";
            // 
            // cmb_currencyTyCd
            // 
            cmb_currencyTyCd.FormattingEnabled = true;
            cmb_currencyTyCd.Items.AddRange(new object[] { "ZMW", "ZAR", "USD", "GBP", "CNY", "EUR" });
            cmb_currencyTyCd.Location = new Point(445, 142);
            cmb_currencyTyCd.Name = "cmb_currencyTyCd";
            cmb_currencyTyCd.Size = new Size(148, 23);
            cmb_currencyTyCd.TabIndex = 52;
            // 
            // cmb_prchrAcptcYn
            // 
            cmb_prchrAcptcYn.FormattingEnabled = true;
            cmb_prchrAcptcYn.Items.AddRange(new object[] { "Y", "N" });
            cmb_prchrAcptcYn.Location = new Point(445, 237);
            cmb_prchrAcptcYn.Name = "cmb_prchrAcptcYn";
            cmb_prchrAcptcYn.Size = new Size(148, 23);
            cmb_prchrAcptcYn.TabIndex = 51;
            // 
            // label39
            // 
            label39.AutoSize = true;
            label39.Location = new Point(315, 245);
            label39.Name = "label39";
            label39.Size = new Size(124, 15);
            label39.TabIndex = 50;
            label39.Text = "Purchaser Acceptance";
            // 
            // cmb_pmtTyCd
            // 
            cmb_pmtTyCd.FormattingEnabled = true;
            cmb_pmtTyCd.Items.AddRange(new object[] { "Cash", "Card" });
            cmb_pmtTyCd.Location = new Point(143, 176);
            cmb_pmtTyCd.Name = "cmb_pmtTyCd";
            cmb_pmtTyCd.Size = new Size(148, 23);
            cmb_pmtTyCd.TabIndex = 38;
            // 
            // dtp_salesDt
            // 
            dtp_salesDt.Location = new Point(143, 234);
            dtp_salesDt.Name = "dtp_salesDt";
            dtp_salesDt.Size = new Size(148, 23);
            dtp_salesDt.TabIndex = 37;
            // 
            // cmb_salesSttsCd
            // 
            cmb_salesSttsCd.FormattingEnabled = true;
            cmb_salesSttsCd.Items.AddRange(new object[] { "Pending", "Confirmed" });
            cmb_salesSttsCd.Location = new Point(143, 205);
            cmb_salesSttsCd.Name = "cmb_salesSttsCd";
            cmb_salesSttsCd.Size = new Size(148, 23);
            cmb_salesSttsCd.TabIndex = 36;
            cmb_salesSttsCd.Visible = false;
            // 
            // cmb_rcptTyCd
            // 
            cmb_rcptTyCd.FormattingEnabled = true;
            cmb_rcptTyCd.Items.AddRange(new object[] { "Sales", "Proforma" });
            cmb_rcptTyCd.Location = new Point(143, 147);
            cmb_rcptTyCd.Name = "cmb_rcptTyCd";
            cmb_rcptTyCd.Size = new Size(148, 23);
            cmb_rcptTyCd.TabIndex = 35;
            cmb_rcptTyCd.Visible = false;
            // 
            // cmb_salesTyCd
            // 
            cmb_salesTyCd.FormattingEnabled = true;
            cmb_salesTyCd.Items.AddRange(new object[] { "Normal", "Return" });
            cmb_salesTyCd.Location = new Point(143, 118);
            cmb_salesTyCd.Name = "cmb_salesTyCd";
            cmb_salesTyCd.Size = new Size(148, 23);
            cmb_salesTyCd.TabIndex = 34;
            // 
            // dtp_cfmDt
            // 
            dtp_cfmDt.Location = new Point(445, 205);
            dtp_cfmDt.Name = "dtp_cfmDt";
            dtp_cfmDt.Size = new Size(148, 23);
            dtp_cfmDt.TabIndex = 45;
            // 
            // txt_regrNm
            // 
            txt_regrNm.Location = new Point(445, 110);
            txt_regrNm.Name = "txt_regrNm";
            txt_regrNm.Size = new Size(148, 23);
            txt_regrNm.TabIndex = 33;
            txt_regrNm.Text = "Jacques";
            // 
            // label33
            // 
            label33.AutoSize = true;
            label33.Location = new Point(325, 118);
            label33.Name = "label33";
            label33.Size = new Size(88, 15);
            label33.TabIndex = 32;
            label33.Text = "Registrar Name";
            // 
            // txt_exchangeRt
            // 
            txt_exchangeRt.Location = new Point(445, 171);
            txt_exchangeRt.Name = "txt_exchangeRt";
            txt_exchangeRt.Size = new Size(148, 23);
            txt_exchangeRt.TabIndex = 30;
            txt_exchangeRt.Text = "1";
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.Location = new Point(23, 184);
            label13.Name = "label13";
            label13.Size = new Size(113, 15);
            label13.TabIndex = 29;
            label13.Text = "Payment Type Code";
            // 
            // label14
            // 
            label14.AutoSize = true;
            label14.Location = new Point(325, 179);
            label14.Name = "label14";
            label14.Size = new Size(83, 15);
            label14.TabIndex = 28;
            label14.Text = "Exchange Rate";
            // 
            // label15
            // 
            label15.AutoSize = true;
            label15.Location = new Point(325, 150);
            label15.Name = "label15";
            label15.Size = new Size(114, 15);
            label15.TabIndex = 25;
            label15.Text = "Currency Type Code";
            // 
            // txt_regrId
            // 
            txt_regrId.Location = new Point(445, 81);
            txt_regrId.Name = "txt_regrId";
            txt_regrId.Size = new Size(148, 23);
            txt_regrId.TabIndex = 22;
            txt_regrId.Text = "1";
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new Point(325, 89);
            label10.Name = "label10";
            label10.Size = new Size(73, 15);
            label10.TabIndex = 20;
            label10.Text = "Registrant Id";
            // 
            // txt_totAmt
            // 
            txt_totAmt.Enabled = false;
            txt_totAmt.Location = new Point(445, 52);
            txt_totAmt.Name = "txt_totAmt";
            txt_totAmt.Size = new Size(148, 23);
            txt_totAmt.TabIndex = 19;
            // 
            // txt_totTaxAmt
            // 
            txt_totTaxAmt.Enabled = false;
            txt_totTaxAmt.Location = new Point(445, 23);
            txt_totTaxAmt.Name = "txt_totTaxAmt";
            txt_totTaxAmt.Size = new Size(148, 23);
            txt_totTaxAmt.TabIndex = 18;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new Point(325, 60);
            label11.Name = "label11";
            label11.Size = new Size(80, 15);
            label11.TabIndex = 17;
            label11.Text = "Total Amount";
            label11.Click += label11_Click;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new Point(325, 31);
            label12.Name = "label12";
            label12.Size = new Size(100, 15);
            label12.TabIndex = 16;
            label12.Text = "Total Tax Amount";
            // 
            // txt_totTaxblAmt
            // 
            txt_totTaxblAmt.Enabled = false;
            txt_totTaxblAmt.Location = new Point(143, 292);
            txt_totTaxblAmt.Name = "txt_totTaxblAmt";
            txt_totTaxblAmt.Size = new Size(148, 23);
            txt_totTaxblAmt.TabIndex = 15;
            // 
            // txt_totItemCnt
            // 
            txt_totItemCnt.Enabled = false;
            txt_totItemCnt.Location = new Point(143, 263);
            txt_totItemCnt.Name = "txt_totItemCnt";
            txt_totItemCnt.Size = new Size(148, 23);
            txt_totItemCnt.TabIndex = 14;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(23, 300);
            label5.Name = "label5";
            label5.Size = new Size(122, 15);
            label5.TabIndex = 13;
            label5.Text = "Total Taxable Amount";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(23, 271);
            label6.Name = "label6";
            label6.Size = new Size(96, 15);
            label6.TabIndex = 12;
            label6.Text = "Total Item Count";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new Point(23, 242);
            label7.Name = "label7";
            label7.Size = new Size(60, 15);
            label7.TabIndex = 9;
            label7.Text = "Sales Date";
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new Point(23, 213);
            label8.Name = "label8";
            label8.Size = new Size(99, 15);
            label8.TabIndex = 8;
            label8.Text = "Sales Status Code";
            label8.Visible = false;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(23, 155);
            label3.Name = "label3";
            label3.Size = new Size(105, 15);
            label3.TabIndex = 5;
            label3.Text = "Receipt Type Code";
            label3.Visible = false;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(23, 126);
            label4.Name = "label4";
            label4.Size = new Size(92, 15);
            label4.TabIndex = 4;
            label4.Text = "Sales Type Code";
            // 
            // txt_bhfId
            // 
            txt_bhfId.Location = new Point(143, 89);
            txt_bhfId.MaxLength = 3;
            txt_bhfId.Name = "txt_bhfId";
            txt_bhfId.Size = new Size(148, 23);
            txt_bhfId.TabIndex = 3;
            txt_bhfId.Text = "000";
            // 
            // txt_tpin
            // 
            txt_tpin.Location = new Point(143, 28);
            txt_tpin.MaxLength = 10;
            txt_tpin.Name = "txt_tpin";
            txt_tpin.Size = new Size(148, 23);
            txt_tpin.TabIndex = 2;
            txt_tpin.Text = "1002087869";
            // 
            // lbl_bhfId
            // 
            lbl_bhfId.AutoSize = true;
            lbl_bhfId.Location = new Point(23, 97);
            lbl_bhfId.Name = "lbl_bhfId";
            lbl_bhfId.Size = new Size(58, 15);
            lbl_bhfId.TabIndex = 1;
            lbl_bhfId.Text = "Branch ID";
            // 
            // lbl_tpin
            // 
            lbl_tpin.AutoSize = true;
            lbl_tpin.Location = new Point(23, 37);
            lbl_tpin.Name = "lbl_tpin";
            lbl_tpin.Size = new Size(64, 15);
            lbl_tpin.TabIndex = 0;
            lbl_tpin.Text = "Seller TPIN";
            // 
            // txt_cisInvcNo
            // 
            txt_cisInvcNo.Enabled = false;
            txt_cisInvcNo.Location = new Point(143, 57);
            txt_cisInvcNo.Name = "txt_cisInvcNo";
            txt_cisInvcNo.Size = new Size(148, 23);
            txt_cisInvcNo.TabIndex = 3;
            // 
            // label31
            // 
            label31.AutoSize = true;
            label31.Location = new Point(23, 65);
            label31.Name = "label31";
            label31.Size = new Size(121, 15);
            label31.TabIndex = 1;
            label31.Text = "CIS Invoice No (Auto)";
            // 
            // label28
            // 
            label28.AutoSize = true;
            label28.Location = new Point(325, 211);
            label28.Name = "label28";
            label28.Size = new Size(105, 15);
            label28.TabIndex = 8;
            label28.Text = "Confirm DateTime";
            // 
            // txt_modrNm
            // 
            txt_modrNm.Location = new Point(933, 163);
            txt_modrNm.Name = "txt_modrNm";
            txt_modrNm.Size = new Size(148, 23);
            txt_modrNm.TabIndex = 26;
            txt_modrNm.Visible = false;
            // 
            // label16
            // 
            label16.AutoSize = true;
            label16.Location = new Point(813, 171);
            label16.Name = "label16";
            label16.Size = new Size(87, 15);
            label16.TabIndex = 24;
            label16.Text = "Modifier Name";
            label16.Visible = false;
            // 
            // txt_modrId
            // 
            txt_modrId.Location = new Point(933, 134);
            txt_modrId.Name = "txt_modrId";
            txt_modrId.Size = new Size(148, 23);
            txt_modrId.TabIndex = 23;
            txt_modrId.Visible = false;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new Point(813, 142);
            label9.Name = "label9";
            label9.Size = new Size(66, 15);
            label9.TabIndex = 21;
            label9.Text = "Modifier ID";
            label9.Visible = false;
            // 
            // lbl_ItemTitle
            // 
            lbl_ItemTitle.AutoSize = true;
            lbl_ItemTitle.Font = new Font("Segoe UI", 15F);
            lbl_ItemTitle.Location = new Point(16, 408);
            lbl_ItemTitle.Name = "lbl_ItemTitle";
            lbl_ItemTitle.Size = new Size(63, 28);
            lbl_ItemTitle.TabIndex = 2;
            lbl_ItemTitle.Text = "Items:";
            // 
            // dgv_Items
            // 
            dgv_Items.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv_Items.Columns.AddRange(new DataGridViewColumn[] { col_sequence, col_itemCode, col_classification_code, col_itemName, col_packagingCode, col_packageQuantity, col_quantityUnitCode, col_quantity, col_unitPrice, col_supplyAmount, col_VATcode, col_totalAmount });
            dgv_Items.Location = new Point(16, 448);
            dgv_Items.Name = "dgv_Items";
            dgv_Items.Size = new Size(1247, 150);
            dgv_Items.TabIndex = 4;
            dgv_Items.CellContentClick += dgv_Items_CellContentClick;
            dgv_Items.RowsAdded += dgv_Items_RowsAdded;
            // 
            // col_sequence
            // 
            col_sequence.HeaderText = "Sequence";
            col_sequence.Name = "col_sequence";
            // 
            // col_itemCode
            // 
            col_itemCode.HeaderText = "Item code";
            col_itemCode.Name = "col_itemCode";
            // 
            // col_classification_code
            // 
            col_classification_code.HeaderText = "Classification code";
            col_classification_code.Name = "col_classification_code";
            // 
            // col_itemName
            // 
            col_itemName.HeaderText = "Item name";
            col_itemName.Name = "col_itemName";
            // 
            // col_packagingCode
            // 
            col_packagingCode.HeaderText = "Packaging Code";
            col_packagingCode.Name = "col_packagingCode";
            // 
            // col_packageQuantity
            // 
            col_packageQuantity.HeaderText = "Package quantity";
            col_packageQuantity.Name = "col_packageQuantity";
            // 
            // col_quantityUnitCode
            // 
            col_quantityUnitCode.HeaderText = "Quantity unit code";
            col_quantityUnitCode.Name = "col_quantityUnitCode";
            // 
            // col_quantity
            // 
            col_quantity.HeaderText = "Quantity";
            col_quantity.Name = "col_quantity";
            // 
            // col_unitPrice
            // 
            col_unitPrice.HeaderText = "Unit price (tax inclusive)";
            col_unitPrice.Name = "col_unitPrice";
            // 
            // col_supplyAmount
            // 
            col_supplyAmount.HeaderText = "Supply amount (qty × prc)";
            col_supplyAmount.Name = "col_supplyAmount";
            // 
            // col_VATcode
            // 
            col_VATcode.HeaderText = "VAT category code";
            col_VATcode.Name = "col_VATcode";
            // 
            // col_totalAmount
            // 
            col_totalAmount.HeaderText = "Total amount";
            col_totalAmount.Name = "col_totalAmount";
            // 
            // btn_AddItem
            // 
            btn_AddItem.Location = new Point(1038, 419);
            btn_AddItem.Name = "btn_AddItem";
            btn_AddItem.Size = new Size(75, 23);
            btn_AddItem.TabIndex = 5;
            btn_AddItem.Text = "Add Item";
            btn_AddItem.UseVisualStyleBackColor = true;
            btn_AddItem.Click += btn_AddItem_Click;
            // 
            // btn_CreateInvoice
            // 
            btn_CreateInvoice.Location = new Point(814, 368);
            btn_CreateInvoice.Name = "btn_CreateInvoice";
            btn_CreateInvoice.Size = new Size(150, 23);
            btn_CreateInvoice.TabIndex = 6;
            btn_CreateInvoice.Text = "Create Invoice";
            btn_CreateInvoice.UseVisualStyleBackColor = true;
            btn_CreateInvoice.Click += btn_CreateInvoice_Click_1;
            // 
            // frmInvoice
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1266, 639);
            Controls.Add(btn_CreateInvoice);
            Controls.Add(btn_AddItem);
            Controls.Add(dgv_Items);
            Controls.Add(lbl_ItemTitle);
            Controls.Add(grbRequired);
            Controls.Add(txt_modrNm);
            Controls.Add(label9);
            Controls.Add(txt_modrId);
            Controls.Add(label16);
            Name = "frmInvoice";
            Text = "Create Invoice";
            Load += frmInvoice_Load_1;
            grbRequired.ResumeLayout(false);
            grbRequired.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgv_Items).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private GroupBox grbRequired;
        private Label label3;
        private Label label4;
        private TextBox txt_bhfId;
        private TextBox txt_tpin;
        private Label lbl_bhfId;
        private Label lbl_tpin;
        private TextBox txt_exchangeRt;
        private Label label13;
        private Label label14;
        private TextBox txt_modrNm;
        private Label label15;
        private Label label16;
        private TextBox txt_modrId;
        private TextBox txt_regrId;
        private Label label9;
        private Label label10;
        private TextBox txt_totAmt;
        private TextBox txt_totTaxAmt;
        private Label label11;
        private Label label12;
        private TextBox txt_totTaxblAmt;
        private TextBox txt_totItemCnt;
        private Label label5;
        private Label label6;
        private Label label7;
        private Label label8;
        private Label label28;
        private TextBox txt_cisInvcNo;
        private Label label31;
        private TextBox txt_regrNm;
        private Label label33;
        private ComboBox cmb_salesSttsCd;
        private ComboBox cmb_rcptTyCd;
        private ComboBox cmb_salesTyCd;
        private DateTimePicker dtp_salesDt;
        private ComboBox cmb_pmtTyCd;
        private DateTimePicker dtp_cfmDt;
        private Label lbl_ItemTitle;
        private DataGridView dgv_Items;
        private Button btn_AddItem;
        private ComboBox cmb_prchrAcptcYn;
        private Label label39;
        private Button btn_CreateInvoice;
        private DataGridViewTextBoxColumn col_sequence;
        private DataGridViewTextBoxColumn col_itemCode;
        private DataGridViewTextBoxColumn col_classification_code;
        private DataGridViewTextBoxColumn col_itemName;
        private DataGridViewTextBoxColumn col_packagingCode;
        private DataGridViewTextBoxColumn col_packageQuantity;
        private DataGridViewTextBoxColumn col_quantityUnitCode;
        private DataGridViewTextBoxColumn col_quantity;
        private DataGridViewTextBoxColumn col_unitPrice;
        private DataGridViewTextBoxColumn col_supplyAmount;
        private DataGridViewTextBoxColumn col_VATcode;
        private DataGridViewTextBoxColumn col_totalAmount;
        private ComboBox cmb_currencyTyCd;
    }
}