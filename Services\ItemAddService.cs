using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zambia.Invoice.Models;
using Zambia.Invoice.Models.Context;
using System.Net.Http;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;

namespace Zambia.Invoice.Services
{
    public class ItemAddService
    {
        private string baseUrl = "http://localhost:8080";
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly ConfigurationService _configService;
        private readonly IServiceProvider _serviceProvider;

        public ItemAddService(IDbContextFactory<AppDbContext> contextFactory, ConfigurationService configService, IServiceProvider serviceProvider)
        {
            _contextFactory = contextFactory;
            _configService = configService;
            _serviceProvider = serviceProvider;
        }

        public async Task<ItemResponse> SaveItemAsync(ItemListEntity request)
        {
            try
            {
                var apiService = new ApiService(baseUrl);
                var credentials = await _configService.GetCredentialsAsync();

                // Prepare the request payload with credentials
                var payload = new
                {
                    tpin = credentials.tpin,
                    bhfId = credentials.bhfId,
                    dvcSrlNo = credentials.dvcSrlNo,
                    lastReqDt = credentials.lastReqDt,
                    itemCd = request.itemCd,
                    itemClsCd = request.itemClsCd,
                    itemTyCd = request.itemTyCd,
                    itemNm = request.itemNm,
                    itemStdNm = request.itemStdNm,
                    orgnNatCd = request.orgnNatCd,
                    pkgUnitCd = request.pkgUnitCd,
                    qtyUnitCd = request.qtyUnitCd,
                    vatCatCd = request.vatCatCd,
                    iplCatCd = request.iplCatCd,
                    tlCatCd = request.tlCatCd,
                    exciseTxCatCd = request.exciseTxCatCd,
                    btchNo = request.btchNo,
                    bcd = request.bcd,
                    dftPrc = request.dftPrc,
                    addInfo = request.addInfo,
                    sftyQty = request.sftyQty,
                    manufacturerTpin = request.manufactuterTpin,
                    manufacturerItemCd = request.manufacturerItemCd,
                    rrp = request.rrp,
                    svcChargeYn = request.svcChargeYn,
                    rentalYn = request.rentalYn,
                    isrcAplcbYn = "N",
                    useYn = request.useYn,
                    regrNm = request.regrNm,
                    regrId = request.regrId,
                    modrNm = request.modrNm,
                    modrId = request.modrId
                };

                ItemResponse itemResponse = await apiService.PostAsync<object, ItemResponse>(
                    "sandboxvsdc/items/saveItem",
                    payload);

                // Save to database
                await SaveItemToDatabase(request, itemResponse);

                return itemResponse;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error saving item: {ex.Message}", ex);
            }
        }

        private async Task SaveItemToDatabase(ItemListEntity request, ItemResponse response)
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // Create ItemResponseEntity for database
                var itemResponseEntity = new ItemResponseEntity
                {
                    Id = Guid.NewGuid(),
                    resultCd = response.resultCd ?? string.Empty,
                    resultMsg = response.resultMsg ?? string.Empty,
                    resultDt = response.resultDt ?? string.Empty
                };

                // Add to context and save
                context.ItemResponses.Add(itemResponseEntity);
                await context.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error saving item data to database: {ex.Message}", ex);
            }
        }

        // Method to retrieve saved requests from database
        public async Task<List<ItemResponseEntity>> GetAllSaveItemRequestsFromDatabase()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ItemResponses.ToListAsync();
        }

        // Method to get count of saved requests
        public async Task<int> GetSaveItemRequestCountAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ItemResponses.CountAsync();
        }
    }
}

