﻿namespace Zambia.Invoice.Forms.ItemForms
{
    partial class ItemForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            dgv_Items = new DataGridView();
            btn_RegreshItems = new Button();
            txt_Search = new TextBox();
            btn_Search = new Button();
            btn_AddItem = new Button();
            ((System.ComponentModel.ISupportInitialize)dgv_Items).BeginInit();
            SuspendLayout();
            // 
            // dgv_Items
            // 
            dgv_Items.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv_Items.Location = new Point(30, 45);
            dgv_Items.Name = "dgv_Items";
            dgv_Items.Size = new Size(1359, 380);
            dgv_Items.TabIndex = 0;
            dgv_Items.CellContentClick += dgv_Items_CellContentClick;
            // 
            // btn_RegreshItems
            // 
            btn_RegreshItems.Location = new Point(30, 12);
            btn_RegreshItems.Name = "btn_RegreshItems";
            btn_RegreshItems.Size = new Size(127, 23);
            btn_RegreshItems.TabIndex = 1;
            btn_RegreshItems.Text = "Refresh Items";
            btn_RegreshItems.UseVisualStyleBackColor = true;
            btn_RegreshItems.Click += btn_RegreshItems_Click;
            // 
            // txt_Search
            // 
            txt_Search.Location = new Point(1120, 16);
            txt_Search.Name = "txt_Search";
            txt_Search.PlaceholderText = "Search..";
            txt_Search.Size = new Size(208, 23);
            txt_Search.TabIndex = 2;
            // 
            // btn_Search
            // 
            btn_Search.Location = new Point(1338, 16);
            btn_Search.Name = "btn_Search";
            btn_Search.Size = new Size(51, 23);
            btn_Search.TabIndex = 3;
            btn_Search.Text = "Search";
            btn_Search.UseVisualStyleBackColor = true;
            btn_Search.Click += btn_Search_Click;
            // 
            // btn_AddItem
            // 
            btn_AddItem.Location = new Point(161, 11);
            btn_AddItem.Name = "btn_AddItem";
            btn_AddItem.Size = new Size(123, 23);
            btn_AddItem.TabIndex = 4;
            btn_AddItem.Text = "Add Item";
            btn_AddItem.UseVisualStyleBackColor = true;
            btn_AddItem.Click += btn_AddItem_Click_1;
            // 
            // ItemForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1412, 450);
            Controls.Add(btn_AddItem);
            Controls.Add(btn_Search);
            Controls.Add(txt_Search);
            Controls.Add(btn_RegreshItems);
            Controls.Add(dgv_Items);
            Name = "ItemForm";
            Text = "Items";
            Load += ItemForm_Load;
            ((System.ComponentModel.ISupportInitialize)dgv_Items).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private DataGridView dgv_Items;
        private Button btn_RegreshItems;
        private TextBox txt_Search;
        private Button btn_Search;
        private Button btn_AddItem;
    }
}