﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Services
{
    public class ApiService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public ApiService(string baseUrl)
        {
            _baseUrl = baseUrl;
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// Sends a POST request to the specified endpoint and deserializes the response to the specified type
        /// </summary>
        /// <typeparam name="TRequest">The type of the request body</typeparam>
        /// <typeparam name="TResponse">The type of the response to deserialize to</typeparam>
        /// <param name="endpoint">The API endpoint (without base URL)</param>
        /// <param name="requestBody">The request body object</param>
        /// <returns>The deserialized response object of type TResponse</returns>
        public async Task<TResponse> PostAsync<TRequest, TResponse>(string endpoint, TRequest requestBody)
        {
            try
            {
                // Serialize the request body to JSON
                string jsonContent = JsonConvert.SerializeObject(requestBody);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // Create the full URL by combining base URL and endpoint
                string url = $"{_baseUrl.TrimEnd('/')}/{endpoint.TrimStart('/')}";

                // Send the POST request
                HttpResponseMessage response = await _httpClient.PostAsync(url, content);

                // Ensure the request was successful
                response.EnsureSuccessStatusCode();

                // Read the response content
                string responseContent = await response.Content.ReadAsStringAsync();

                // Print raw JSON for debugging
                Console.WriteLine("Raw JSON response:");
                Console.WriteLine(responseContent);

                // Use settings to handle deserialization issues
                var settings = new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore,
                    MissingMemberHandling = MissingMemberHandling.Ignore,
                    Error = (sender, args) =>
                    {
                        Console.WriteLine($"Deserialization Error: {args.ErrorContext.Error.Message}");
                        Console.WriteLine($"Error Path: {args.ErrorContext.Path}");
                        args.ErrorContext.Handled = true;
                    }
                };

                // Deserialize to the specified TResponse type
                TResponse result = JsonConvert.DeserializeObject<TResponse>(responseContent, settings);

                // Check if deserialization was successful
                if (result != null)
                {
                    Console.WriteLine($"Deserialization successful for type: {typeof(TResponse).Name}");
                }
                else
                {
                    Console.WriteLine($"Deserialization resulted in null object for type: {typeof(TResponse).Name}");
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in API call: {ex.Message}");
                throw;
            }
        }
    }
}
