﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Models
{
    public class ItemCodesModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; } // Auto-generated Primary Key

        public string CountryOfOrigin { get; set; } = string.Empty; // e.g., "ZM" for Zambia
        public int ProductType { get; set; }       // e.g., 2 for Finished Product
        public string PackagingUnit { get; set; } = string.Empty;   // e.g., "NT" for NET
        public string QuantityUnit { get; set; } = string.Empty;    // e.g., "BA" for Barrel

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int IncrementValue { get; set; }    // Auto-incremented value

        // Computed property for full ItemCode
        [NotMapped]
        public string ItemCode => $"{CountryOfOrigin}{ProductType:D1}{PackagingUnit}{QuantityUnit}{IncrementValue:D7}";
    }
}
