using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Helpers
{


    public static class IconHelper
    {
        public static Icon? LoadApplicationIcon()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var resourceName = "Zambia.Invoice.Assets.PRIMEICON.ico";

                using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream != null)
                {
                    return new Icon(stream);
                }
            }
            catch { }
            return null;
        }

        public static void ApplyIconToAllForms(Icon icon)
        {
            foreach (Form form in Application.OpenForms)
            {
                form.Icon = icon;
            }
        }

        //private static Icon? _applicationIcon;

        //public static void Initialize()
        //{
        //    try
        //    {
        //        var assembly = Assembly.GetExecutingAssembly();
        //        var resourceName = "Zambia.Invoice.Assets.PRIMEICON.ico";

        //        using var stream = assembly.GetManifestResourceStream(resourceName);
        //        if (stream != null)
        //        {
        //            _applicationIcon = new Icon(stream);
        //        }
        //        else
        //        {
        //            // Debug: Show all resources if not found
        //            var resources = assembly.GetManifestResourceNames();
        //            MessageBox.Show($"Icon not found. Available resources:\n{string.Join("\n", resources)}");
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show($"Error loading icon: {ex.Message}");
        //    }

        //    // Hook into all forms
        //    if (_applicationIcon != null)
        //    {
        //        Application.AddMessageFilter(new FormIconSetter());
        //    }
        //}

        //private class FormIconSetter : IMessageFilter
        //{
        //    public bool PreFilterMessage(ref Message m)
        //    {
        //        // WM_CREATE = 0x0001
        //        if (m.Msg == 0x0001 && _applicationIcon != null)
        //        {
        //            if (Control.FromHandle(m.HWnd) is Form form)
        //            {
        //                form.Icon = _applicationIcon;
        //            }
        //        }
        //        return false;
        //    }
        //}
    }
}
