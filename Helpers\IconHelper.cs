
namespace Zambia.Invoice.Helpers
{
    public static class IconHelper
    {
        private static Icon? _applicationIcon;
        private static Icon? _globalIcon;
        private static System.Windows.Forms.Timer? _iconTimer;

        public static Icon? LoadApplicationIcon()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var resourceName = "Zambia.Invoice.Assets.PRIMEICON.ico";

                using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream != null)
                {
                    return new Icon(stream);
                }
            }
            catch { }
            return null;
        }

        public static void StartIconMonitoring(Icon? icon)
        {
            _globalIcon = icon;
            if (_globalIcon == null) return;

            // Apply to currently open forms
            ApplyIconToAllForms(icon);

            // Start timer to check for new forms every 100ms
            _iconTimer = new System.Windows.Forms.Timer();
            _iconTimer.Interval = 100;
            _iconTimer.Tick += (s, e) => ApplyIconToAllForms(_globalIcon);
            _iconTimer.Start();

            // Stop timer when application exits
            Application.ApplicationExit += (s, e) => {
                _iconTimer?.Stop();
                _iconTimer?.Dispose();
            };
        }

        public static void ApplyIconToAllForms(Icon? icon = null)
        {
            var iconToUse = icon ?? _applicationIcon;
            if (iconToUse == null) return;

            foreach (Form form in Application.OpenForms)
            {
                if (form.Icon != iconToUse)
                {
                    form.Icon = iconToUse;
                }
            }
        }
    }
}
