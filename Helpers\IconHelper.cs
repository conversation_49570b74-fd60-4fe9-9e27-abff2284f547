using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Zambia.Invoice.Helpers
{


    public static class IconHelper
    {
        private static Icon _applicationIcon;

        public static void InitializeIcon()
        {
            try
            {
                _applicationIcon = new Icon("Resources/icon.ico");
            }
            catch
            {
                _applicationIcon = null;
            }
        }

        public static void ApplyIconToAllForms()
        {
            if (_applicationIcon == null) return;

            // Apply to all currently open forms
            foreach (Form form in Application.OpenForms)
            {
                form.Icon = _applicationIcon;
            }

            // Subscribe to new form creation
            Application.ApplicationContext.MainForm.HandleCreated += (s, e) => ApplyIconToAllForms();
        }

        public static void ApplyIconToForm(Form form)
        {
            if (_applicationIcon != null && form != null)
            {
                form.Icon = _applicationIcon;
            }
        }
    }
}
