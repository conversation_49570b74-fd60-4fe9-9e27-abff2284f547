namespace Zambia.Invoice.Helpers
{
    public static class IconHelper
    {
        private static Icon? _applicationIcon;

        public static Icon? LoadApplicationIcon()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var resourceName = "Zambia.Invoice.Assets.PRIMEICON.ico";

                using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream != null)
                {
                    return new Icon(stream);
                }
            }
            catch { }
            return null;
        }

        public static void InitializeIcon()
        {
            try
            {
                _applicationIcon = new Icon("Resources/icon.ico");
            }
            catch
            {
                _applicationIcon = null;
            }
        }

        public static void ApplyIconToAllForms()
        {
            if (_applicationIcon == null) return;

            // Apply to all currently open forms
            foreach (Form form in Application.OpenForms)
            {
                form.Icon = _applicationIcon;
            }

            // Note: Form creation subscription is handled in Program.cs
        }

        public static void ApplyIconToForm(Form form)
        {
            if (_applicationIcon != null && form != null)
            {
                form.Icon = _applicationIcon;
            }
        }
    }
}
