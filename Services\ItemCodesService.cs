using Microsoft.EntityFrameworkCore;
using Zambia.Invoice.Models;
using Zambia.Invoice.Models.Context;

namespace Zambia.Invoice.Services
{
    public class ItemCodesService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public ItemCodesService(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<ItemCodesModel> CreateItemCodeAsync(string countryOfOrigin, int productType, string packagingUnit, string quantityUnit)
        {
            using var context = _contextFactory.CreateDbContext();

            var itemCode = new ItemCodesModel
            {
                CountryOfOrigin = countryOfOrigin,
                ProductType = productType,
                PackagingUnit = packagingUnit,
                QuantityUnit = quantityUnit
            };

            context.ItemCodes.Add(itemCode);
            await context.SaveChangesAsync();

            return itemCode;
        }

        public async Task<List<ItemCodesModel>> GetAllItemCodesAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ItemCodes.ToListAsync();
        }

        public async Task<ItemCodesModel?> GetItemCodeByIdAsync(Guid id)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ItemCodes.FindAsync(id);
        }

        public async Task<ItemCodesModel?> GetHighestIncrementValueAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ItemCodes
                .AsNoTracking()
                .OrderByDescending(ic => ic.IncrementValue)
                .FirstOrDefaultAsync();
        }
    }
}







