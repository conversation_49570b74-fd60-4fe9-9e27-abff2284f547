﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Zambia.Invoice.Helpers;
using Zambia.Invoice.Models;
using Zambia.Invoice.Services;

namespace Zambia.Invoice.Forms.ClassForms
{
    public partial class frmClass : Form
    {
        private readonly ClassService _classService;
        private List<ItemClsListEntity> _allClasses = new List<ItemClsListEntity>();

        public frmClass(ClassService classService)
        {
            InitializeComponent();
            //IconHelper.ApplyIcon(this);
            _classService = classService;
        }

        private async void frmClass_Load(object sender, EventArgs e)
        {
            await LoadClassData();
            txtFilter.TextChanged += TxtFilter_TextChanged;
        }

        private async Task LoadClassData()
        {
            try
            {
                var classData = await _classService.GetAllClassesFromDatabase();

                // Flatten the data to show all ItemClsListEntity items
                _allClasses = classData
                    .SelectMany(c => c.itemClsList)
                    .ToList();

                dataGridView.DataSource = _allClasses;

                if (dataGridView.Columns.Count > 0)
                {
                    dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading class data: {ex.Message}");
            }
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            string filterText = txtFilter.Text.Trim().ToLower();

            if (string.IsNullOrEmpty(filterText))
            {
                dataGridView.DataSource = _allClasses;
            }
            else
            {
                var filtered = _allClasses
                    .Where(cls =>
                        (!string.IsNullOrEmpty(cls.itemClsCd) && cls.itemClsCd.ToLower().Contains(filterText)) ||
                        (!string.IsNullOrEmpty(cls.itemClsNm) && cls.itemClsNm.ToLower().Contains(filterText))
                    ).ToList();

                dataGridView.DataSource = filtered;
            }

            dataGridView.Refresh();
        }

        private void txtFilter_TextChanged_1(object sender, EventArgs e)
        {

        }

        private void dataGridView_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }


    }
}
