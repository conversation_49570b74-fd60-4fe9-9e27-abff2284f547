﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Models
{


    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);

    // POCO classes for JSON deserialization only - NOT mapped to database
    public class DataClasses
    {
        public List<ItemClsList> itemClsList { get; set; }
    }

    // This ItemClsList is for deserialization - will have a separate DB entity
    public class ItemClsList
    {
        public string itemClsCd { get; set; }
        public string itemClsNm { get; set; }
        public int itemClsLvl { get; set; }
        public object taxTyCd { get; set; }
        public object mjrTgYn { get; set; }
        public string useYn { get; set; }
    }

    // This ClassModel is for deserialization - will have a separate DB entity
    public class ClassModel
    {
        public string resultCd { get; set; }
        public string resultMsg { get; set; }
        public string resultDt { get; set; }
        public DataClasses data { get; set; }
    }

    // Database entities below (separate from deserialization classes):

    public class ClassModelEntity
    {
        [Key]
        public Guid Id { get; set; } // Primary Key
        public string resultCd { get; set; }
        public string resultMsg { get; set; }
        public string resultDt { get; set; }

        // Navigation property - one ClassModelEntity can have many ItemClsListEntity
        public List<ItemClsListEntity> itemClsList { get; set; }
    }

    public class ItemClsListEntity
    {
        [Key]
        public Guid Id { get; set; } // Primary Key
        public string itemClsCd { get; set; }
        public string itemClsNm { get; set; }
        public int itemClsLvl { get; set; }
        public string taxTyCd { get; set; } // Changed from object to string for DB
        public string mjrTgYn { get; set; } // Changed from object to string for DB
        public string useYn { get; set; }

        // Foreign Key to ClassModelEntity
        public Guid ClassModelEntityId { get; set; }
        [ForeignKey("ClassModelEntityId")]
        public ClassModelEntity ClassModel { get; set; }
    }
}
