﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Global
{

    public static class GlobalConfig
    {
        // Connection string for database access
        public static string ConnectionString { get; set; } = 
            //"Server=************\\TEST;Initial Catalog=ZambiaInvoice;User Id=Zambia;Password=********;MultipleActiveResultSets=True;TrustServerCertificate=True;";
            "Server=************\\TEST;Initial Catalog=Zambia;User Id=Zambia;Password=********;MultipleActiveResultSets=True;TrustServerCertificate=True;";

        // Device serial number
        public static string DeviceSerialNumber { get; set; } = "CND1438TQP";

        // Branch code
        public static string BranchCode { get; set; } = "001";

        public static string Tpin   { get; set; } = "1002087869";

        //// Add other global variables as needed
        //public static string Tpin { get; set; }
        //public static string LastRequestDate { get; set; }
    }

}
