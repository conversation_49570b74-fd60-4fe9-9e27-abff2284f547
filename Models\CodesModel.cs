﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Models
{

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);

    // Data class for JSON deserialization only - NOT mapped to database
    [NotMapped]
    public class Data
    {
        public List<ClsList> clsList { get; set; } = new List<ClsList>();
    }

    // Database entities below:
    public class CodesModel
    {
        [Key]
        public Guid Id { get; set; } // Primary Key

        public string resultCd { get; set; } = string.Empty;
        public string resultMsg { get; set; } = string.Empty;
        public string resultDt { get; set; } = string.Empty;

        // Navigation property - one CodesModel can have many ClsList
        // Remove the Data property and directly reference ClsList
        public List<ClsList> ClsList { get; set; } = new List<ClsList>();

        // For JSON deserialization, you can use this property
        [NotMapped]
        public Data? data
        {
            get => new Data { clsList = ClsList };
            set => ClsList = value?.clsList ?? new List<ClsList>();
        }
    }

    public class ClsList
    {
        [Key]
        public Guid Id { get; set; } // Primary Key

        public string cdCls { get; set; } = string.Empty;
        public string cdClsNm { get; set; } = string.Empty;
        public string userDfnNm1 { get; set; } = string.Empty;

        // Foreign Key to CodesModel
        public Guid CodesModelId { get; set; }

        [ForeignKey("CodesModelId")]
        public CodesModel? CodesModel { get; set; }

        // Navigation property - one ClsList can have many DtlList
        public List<DtlList> dtlList { get; set; } = new List<DtlList>();
    }

    public class DtlList
    {
        [Key]
        public Guid Id { get; set; } // Primary Key

        public string cd { get; set; } = string.Empty;
        public string cdNm { get; set; } = string.Empty;
        public string userDfnCd1 { get; set; } = string.Empty;

        // Foreign Key to ClsList
        public Guid ClsListId { get; set; }

        [ForeignKey("ClsListId")]
        public ClsList? ClsList { get; set; }
    }

}
