﻿using System.ComponentModel;
using System.Diagnostics;
using System.Windows.Forms;
using Zambia.Invoice.Global;
using Zambia.Invoice.Models;
using Zambia.Invoice.Models.LocalModels;
using Zambia.Invoice.Services;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Tab;

namespace Zambia.Invoice.Forms.ItemForms
{
    public partial class frm_AddItem : Form
    {
        private readonly ItemService _itemService;
        private readonly ItemCodesService _itemCodesService;
        private readonly ItemAddService _itemAddService;

        public frm_AddItem(ItemService itemService, ItemCodesService itemCodesService, ItemAddService itemAddService)
        {
            InitializeComponent();
            _itemService = itemService;
            _itemCodesService = itemCodesService;
            _itemCodesService = itemCodesService;
            _itemAddService = itemAddService;
            // Manually wire up the Load event if it's not working
            //this.Load += frm_AddItem_Load;

            //InitializeComponent();

            // this.Load += frm_AddItem_Load;



        }



        private async void frm_AddItem_Load(object sender, EventArgs e)
        {
            toolTip_USNPSC.SetToolTip(llb_itemClsCd, "Smart Invoice utilizes the United Nations Standard Products and Services Code\r\n(UNSPSC) as the standardized classification system for products and services.. \r\n\r\nRetrieve this from the Item classification code dictionary under the Smart Invoice Tab on\r\nthe ZRA website");

            // Create a new ItemCode to get the next increment value
            var newItemCode = await _itemCodesService.CreateItemCodeAsync("ZM", 2, "AD", "FR");
            txt_itemCd.Text = newItemCode.ItemCode;

            // Setup rentalYn dropdown
            cmb_rentalYn.DropDownStyle = ComboBoxStyle.DropDownList;
            cmb_rentalYn.DisplayMember = "Text";
            cmb_rentalYn.ValueMember = "Value";
            // Clear any existing items first (optional but good practice)
            cmb_rentalYn.Items.Clear();
            cmb_rentalYn.Items.Add(new DropDownValueText { Text = "No", Value = "N" });
            cmb_rentalYn.Items.Add(new DropDownValueText { Text = "Yes", Value = "Y" });
            cmb_rentalYn.SelectedIndex = 0; // <-- this must come after adding items

            // Setup svcChargeYn dropdown
            cmb_svcChargeYn.DropDownStyle = ComboBoxStyle.DropDownList;
            cmb_svcChargeYn.DisplayMember = "Text";
            cmb_svcChargeYn.ValueMember = "Value";
            // Clear any existing items first (optional but good practice)
            cmb_svcChargeYn.Items.Clear();
            cmb_svcChargeYn.Items.Add(new DropDownValueText { Text = "No", Value = "N" });
            cmb_svcChargeYn.Items.Add(new DropDownValueText { Text = "Yes", Value = "Y" });
            cmb_svcChargeYn.SelectedIndex = 0; // <-- this must come after adding items 


            // Setup cmb_itemTyCd dropdown
            cmb_itemTyCd.DropDownStyle = ComboBoxStyle.DropDownList;
            cmb_itemTyCd.DisplayMember = "Text";
            cmb_itemTyCd.ValueMember = "Value";
            cmb_itemTyCd.Items.Clear();
            cmb_itemTyCd.Items.Add(new DropDownValueText { Text = "Raw Material", Value = "1" });
            cmb_itemTyCd.Items.Add(new DropDownValueText { Text = "Finished Product", Value = "2" });
            cmb_itemTyCd.Items.Add(new DropDownValueText { Text = "Service", Value = "3" });
            cmb_itemTyCd.SelectedIndex = 1; // For "Finished Product" (Value = "2")

            // Setup cmb_useYn dropdown
            cmb_useYn.DropDownStyle = ComboBoxStyle.DropDownList;
            cmb_useYn.DisplayMember = "Text";
            cmb_useYn.ValueMember = "Value";
            cmb_useYn.Items.Clear();
            cmb_useYn.Items.Add(new DropDownValueText { Text = "Yes", Value = "Y" });
            cmb_useYn.Items.Add(new DropDownValueText { Text = "No", Value = "N" });
            cmb_useYn.SelectedIndex = 0; // Default to "Yes"

            // Setup cmb_orgnNatCd dropdown
            cmb_orgnNatCd.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
            cmb_orgnNatCd.AutoCompleteSource = AutoCompleteSource.ListItems;
            cmb_orgnNatCd.DropDownStyle = ComboBoxStyle.DropDown;

            // Bind the static list to the ComboBox
            cmb_orgnNatCd.DataSource = CountryCodes.CountryList;
            cmb_orgnNatCd.DisplayMember = "Text";  // This will show the country name
            cmb_orgnNatCd.ValueMember = "Value";   // This will be the country code

            // Optional: Set initial selection to empty
            // Set to Zimbabwe
            cmb_orgnNatCd.SelectedValue = "ZM";

            // To get the selected country code when needed:
            // string selectedCountryCode = cmb_orgnNatCd.SelectedValue?.ToString();

            // To get the selected country name when needed:
            // string selectedCountryName = cmb_orgnNatCd.Text;


            //Setup packaging unit dropdown
            cmb_pkgUnitCd.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
            cmb_pkgUnitCd.AutoCompleteSource = AutoCompleteSource.ListItems;
            cmb_pkgUnitCd.DropDownStyle = ComboBoxStyle.DropDown;

            // Bind the static list to the ComboBox
            cmb_pkgUnitCd.DataSource = PackagingUnits.PackagingUnitList;
            cmb_pkgUnitCd.DisplayMember = "Text";  // This will show the packaging unit name
            cmb_pkgUnitCd.ValueMember = "Value";   // This will be the packaging unit code

            // Optional: Set initial selection to empty
            cmb_pkgUnitCd.SelectedValue = "P/KG";

            //Setup units of measure dropdown
            cmb_qtyUnitCd.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
            cmb_qtyUnitCd.AutoCompleteSource = AutoCompleteSource.ListItems;
            cmb_qtyUnitCd.DropDownStyle = ComboBoxStyle.DropDown;

            // Bind the static list to the ComboBox
            cmb_qtyUnitCd.DataSource = UnitsOfMeasure.UnitsOfMeasureList;
            cmb_qtyUnitCd.DisplayMember = "Text";  // This will show the unit description
            cmb_qtyUnitCd.ValueMember = "Value";   // This will be the unit code

            // Optional: Set initial selection to empty
            cmb_qtyUnitCd.SelectedValue = "MTR";

            // Example: To set it to "Pieces/item [Number]"
            // cmb_qtyUnitCd.SelectedValue = "U";


            //Setup VAT tax type dropdown vatCatCd
            cmb_vatCatCd.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
            cmb_vatCatCd.AutoCompleteSource = AutoCompleteSource.ListItems;
            cmb_vatCatCd.DropDownStyle = ComboBoxStyle.DropDown;

            cmb_vatCatCd.DataSource = VATTaxTypes.VATList;
            cmb_vatCatCd.DisplayMember = "Text";
            cmb_vatCatCd.ValueMember = "Value";
            cmb_vatCatCd.SelectedIndex = 0;

            //Setup IPL tax type dropdown
            cmb_iplCatCd.DataSource = InsurancePremiumLevyTaxTypes.IPLList;
            cmb_iplCatCd.DisplayMember = "Text";
            cmb_iplCatCd.ValueMember = "Value";
            cmb_iplCatCd.SelectedIndex = -1;

            //Setup TL tax type dropdown
            cmb_tlCatCd.DataSource = TourismLevyTaxTypes.TLList;
            cmb_tlCatCd.DisplayMember = "Text";
            cmb_tlCatCd.ValueMember = "Value";
            cmb_tlCatCd.SelectedIndex = -1;

            //Setup Excise tax type dropdown
            cmb_exciseTxCatCd.DataSource = ExciseTaxTypes.ExciseList;
            cmb_exciseTxCatCd.DisplayMember = "Text";
            cmb_exciseTxCatCd.ValueMember = "Value";
            cmb_exciseTxCatCd.SelectedIndex = -1;

            txt_bhfId.Text = GlobalConfig.BranchCode;

        }

        private void textBox5_TextChanged(object sender, EventArgs e)
        {

        }

        private void txt_dftPrc_TextChanged(object sender, EventArgs e)
        {

        }
        private void txt_dftPrc_Validating(object sender, CancelEventArgs e)
        {
            string text = txt_dftPrc.Text.Trim();

            // Use InvariantCulture to avoid locale issues with decimal separator
            if (!decimal.TryParse(text, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out decimal value))
            {
                e.Cancel = true;
                MessageBox.Show("Invalid number.");
                return;
            }

            string[] parts = text.Split('.');
            string integerPart = parts[0].TrimStart('0'); // '000123' becomes '123'
            string decimalPart = parts.Length > 1 ? parts[1] : "";

            int totalDigits = integerPart.Length + decimalPart.Length;
            int decimalPlaces = decimalPart.Length;

            // Allow numbers like 0.0001 (4 decimal places, total digits = 4)
            if (totalDigits > 18 || decimalPlaces > 4)
            {
                e.Cancel = true;
                MessageBox.Show("Value must have up to 18 digits in total and no more than 4 decimal places.");
            }
        }



        private void txt_dftPrc_KeyPress(object sender, KeyPressEventArgs e)
        {
            TextBox textBox = sender as TextBox;

            // Allow control keys (like Backspace)
            if (char.IsControl(e.KeyChar))
                return;

            // Allow only one dot (.)
            if (e.KeyChar == '.' && textBox.Text.Contains('.'))
            {
                e.Handled = true; // block input
                return;
            }

            // Allow only digits and decimal point
            if (!char.IsDigit(e.KeyChar) && e.KeyChar != '.')
            {
                e.Handled = true;
                return;
            }

            // Predict resulting text after key is pressed
            string resultText = textBox.Text.Insert(textBox.SelectionStart, e.KeyChar.ToString());

            var parts = resultText.Split('.');
            int totalDigits = parts[0].Length + (parts.Length > 1 ? parts[1].Length : 0);
            int decimals = parts.Length > 1 ? parts[1].Length : 0;

            if (totalDigits > 18 || decimals > 4)
            {
                e.Handled = true;
            }
        }

        private void txt_rrp_TextChanged(object sender, EventArgs e)
        {

        }
        private void txt_rrp_Validating(object sender, CancelEventArgs e)
        {
            string text = txt_dftPrc.Text.Trim();

            // Use InvariantCulture to avoid locale issues with decimal separator
            if (!decimal.TryParse(text, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out decimal value))
            {
                e.Cancel = true;
                MessageBox.Show("Invalid number.");
                return;
            }

            string[] parts = text.Split('.');
            string integerPart = parts[0].TrimStart('0'); // '000123' becomes '123'
            string decimalPart = parts.Length > 1 ? parts[1] : "";

            int totalDigits = integerPart.Length + decimalPart.Length;
            int decimalPlaces = decimalPart.Length;

            // Allow numbers like 0.0001 (4 decimal places, total digits = 4)
            if (totalDigits > 18 || decimalPlaces > 4)
            {
                e.Cancel = true;
                MessageBox.Show("Value must have up to 18 digits in total and no more than 4 decimal places.");
            }
        }



        private void txt_rrp_KeyPress(object sender, KeyPressEventArgs e)
        {
            TextBox textBox = sender as TextBox;

            // Allow control keys (like Backspace)
            if (char.IsControl(e.KeyChar))
                return;

            // Allow only one dot (.)
            if (e.KeyChar == '.' && textBox.Text.Contains('.'))
            {
                e.Handled = true; // block input
                return;
            }

            // Allow only digits and decimal point
            if (!char.IsDigit(e.KeyChar) && e.KeyChar != '.')
            {
                e.Handled = true;
                return;
            }

            // Predict resulting text after key is pressed
            string resultText = textBox.Text.Insert(textBox.SelectionStart, e.KeyChar.ToString());

            var parts = resultText.Split('.');
            int totalDigits = parts[0].Length + (parts.Length > 1 ? parts[1].Length : 0);
            int decimals = parts.Length > 1 ? parts[1].Length : 0;

            if (totalDigits > 18 || decimals > 4)
            {
                e.Handled = true;
            }
        }

        private void grp_Optional_Enter(object sender, EventArgs e)
        {

        }

        private void cmb_rentalYn_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selectedValue = ((DropDownValueText)cmb_rentalYn.SelectedItem).Value;
        }

        private void cmb_useYn_SelectedIndexChanged(object sender, EventArgs e)
        {

        }


        private void llb_itemClsCd_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            System.Diagnostics.Process.Start(new ProcessStartInfo
            {
                FileName = "https://sandboxportal.zra.org.zm/app/ebm/item/indexItemClass",
                UseShellExecute = true
            });
        }

        private void toolTip_USNPSC_Popup(object sender, PopupEventArgs e)
        {

        }

        private void lbl_orgnNatCd_Click(object sender, EventArgs e)
        {

        }

        private async void btn_AddItem_Click(object sender, EventArgs e)
        {
            LoadingForm loadingForm = null;
            try
            {
                loadingForm = new LoadingForm();
                loadingForm.Show(this);

                // Debug the ComboBox selection
                MessageBox.Show($"Selected Index: {cmb_itemTyCd.SelectedIndex}, Selected Item: {cmb_itemTyCd.SelectedItem}, Selected Value: {cmb_itemTyCd.SelectedValue}");

                var selectedItem = cmb_itemTyCd.SelectedItem as DropDownValueText;
                MessageBox.Show($"Cast result: {selectedItem?.Value ?? "NULL"}");

                // Validate required fields first
                if (!ValidateRequiredFields())
                {
                    return; // Validation failed, error message already shown
                }

                // Create ItemListEntity and populate from form controls
                ItemListEntity itemListEntity = new ItemListEntity();

                // Required fields - these must have values
                itemListEntity.itemCd = txt_itemCd.Text.Trim();
                itemListEntity.itemClsCd = txt_itemClsCd.Text.Trim();
                itemListEntity.itemTyCd = ((DropDownValueText)cmb_itemTyCd.SelectedItem)?.Value;

                // Debug what we're actually setting
                MessageBox.Show($"itemTyCd being set to: {itemListEntity.itemTyCd ?? "NULL"}");

                itemListEntity.itemNm = txt_itemNm.Text.Trim();
                itemListEntity.itemStdNm = string.IsNullOrWhiteSpace(txt_itemStdNm.Text) ? itemListEntity.itemNm : txt_itemStdNm.Text.Trim(); // Use itemNm if itemStdNm is empty
                itemListEntity.orgnNatCd = cmb_orgnNatCd.SelectedValue?.ToString();
                itemListEntity.pkgUnitCd = cmb_pkgUnitCd.SelectedValue?.ToString();
                itemListEntity.qtyUnitCd = cmb_qtyUnitCd.SelectedValue?.ToString();
                itemListEntity.vatCatCd = cmb_vatCatCd.SelectedValue?.ToString();
                itemListEntity.useYn = ((DropDownValueText)cmb_useYn.SelectedItem)?.Value;
                itemListEntity.regrNm = txt_regrNm.Text.Trim();
                itemListEntity.regrId = txt_regrId.Text.Trim();
                itemListEntity.modrNm = txt_modrNm.Text.Trim();
                itemListEntity.modrId = txt_modrId.Text.Trim();

                // Optional fields - can be empty/null
                itemListEntity.btchNo = string.IsNullOrWhiteSpace(txt_btchNo.Text) ? null : txt_btchNo.Text.Trim();
                itemListEntity.bcd = string.IsNullOrWhiteSpace(txt_bcd.Text) ? null : txt_bcd.Text.Trim();
                itemListEntity.addInfo = string.IsNullOrWhiteSpace(rtb_addInfo.Text) ? null : rtb_addInfo.Text.Trim();
                itemListEntity.manufactuterTpin = string.IsNullOrWhiteSpace(txt_manufactuterTpin.Text) ? null : txt_manufactuterTpin.Text.Trim();
                itemListEntity.manufacturerItemCd = string.IsNullOrWhiteSpace(txt_manufacturerItemCd.Text) ? null : txt_manufacturerItemCd.Text.Trim();
                itemListEntity.svcChargeYn = ((DropDownValueText)cmb_svcChargeYn.SelectedItem)?.Value;
                itemListEntity.rentalYn = ((DropDownValueText)cmb_rentalYn.SelectedItem)?.Value;

                // Parse numeric fields and convert to integers
                if (int.TryParse(txt_dftPrc.Text.Trim(), out int dftPrcValue))
                    itemListEntity.dftPrc = dftPrcValue;
                else
                    itemListEntity.dftPrc = 0;

                itemListEntity.sftyQty = (int)numericUpDown1.Value;

                // Possibly null tax category fields
                itemListEntity.iplCatCd = cmb_iplCatCd.SelectedValue?.ToString();
                itemListEntity.tlCatCd = cmb_tlCatCd.SelectedValue?.ToString();
                itemListEntity.exciseTxCatCd = cmb_exciseTxCatCd.SelectedValue?.ToString();

                // Set ID
                itemListEntity.Id = Guid.NewGuid();

                // Get ItemAddService and call SaveItemAsync

                var response = await _itemAddService.SaveItemAsync(itemListEntity);

                if (response != null && response.resultCd == "000")
                {
                    MessageBox.Show("Item saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.Close();
                }
                else
                {
                    MessageBox.Show($"Failed to save item: {response?.resultMsg ?? "Unknown error"}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving item: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                loadingForm?.Close();
            }
        }

        private bool ValidateRequiredFields()
        {
            var requiredFields = new List<(Control control, string fieldName)>
            {
                (txt_itemCd, "Item Code"),
                (txt_itemClsCd, "Item Classification Code"),
                (cmb_itemTyCd, "Item Type"),
                (txt_itemNm, "Item Name"),
                (cmb_orgnNatCd, "Country of Origin"),
                (cmb_pkgUnitCd, "Packaging Unit"),
                (cmb_qtyUnitCd, "Quantity Unit"),
                (cmb_vatCatCd, "VAT Category"),
                (cmb_useYn, "Use Status"),
                (txt_regrNm, "Registrant Name"),
                (txt_regrId, "Registrant ID"),
                (txt_modrNm, "Modifier Name"),
                (txt_modrId, "Modifier ID")
            };

            var missingFields = new List<string>();

            foreach (var (control, fieldName) in requiredFields)
            {
                bool isEmpty = false;

                if (control is TextBox textBox)
                {
                    isEmpty = string.IsNullOrWhiteSpace(textBox.Text);
                }
                else if (control is ComboBox comboBox)
                {
                    // For ComboBoxes with DropDownValueText items, check SelectedItem instead
                    if (comboBox == cmb_itemTyCd || comboBox == cmb_useYn)
                    {
                        isEmpty = comboBox.SelectedItem == null || comboBox.SelectedIndex == -1;
                    }
                    else
                    {
                        // For other ComboBoxes (country, packaging, etc.)
                        isEmpty = comboBox.SelectedIndex == -1 ||
                                 comboBox.SelectedValue == null ||
                                 string.IsNullOrWhiteSpace(comboBox.SelectedValue.ToString());
                    }
                }

                if (isEmpty)
                {
                    missingFields.Add(fieldName);
                }
            }

            if (missingFields.Any())
            {
                string message = "The following required fields are missing:\n\n" + string.Join("\n", missingFields);
                MessageBox.Show(message, "Required Fields Missing", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void txt_itemClsCd_TextChanged(object sender, EventArgs e)
        {

        }

        private void txt_regrNm_TextChanged(object sender, EventArgs e)
        {

        }
    }
}
