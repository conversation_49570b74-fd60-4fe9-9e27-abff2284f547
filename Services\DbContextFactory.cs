using Microsoft.EntityFrameworkCore;
using Zambia.Invoice.Models.Context;

namespace Zambia.Invoice.Services
{
    public interface IDbContextFactory
    {
        AppDbContext CreateDbContext();
    }

    public class DbContextFactory : IDbContextFactory
    {
        private readonly string _connectionString;

        public DbContextFactory(string connectionString)
        {
            _connectionString = connectionString;
        }

        public AppDbContext CreateDbContext()
        {
            var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
            optionsBuilder.UseSqlServer(_connectionString);
            return new AppDbContext(optionsBuilder.Options);
        }
    }
}
