using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Zambia.Invoice.Forms;
using Zambia.Invoice.Forms.ClassForms;
using Zambia.Invoice.Forms.CodesForms;
using Zambia.Invoice.Forms.InvoiceForms;
using Zambia.Invoice.Forms.ItemForms;
using Zambia.Invoice.Global;
using Zambia.Invoice.Helpers;
using Zambia.Invoice.Models.Context;
using Zambia.Invoice.Services;

namespace Zambia.Invoice
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            var host = CreateHostBuilder().Build();
            ApplicationConfiguration.Initialize();
           
            // Create a scope for the main form to ensure proper DbContext lifetime
            using var scope = host.Services.CreateScope();
            var mainForm = scope.ServiceProvider.GetRequiredService<frmMain>(); // DI-resolved form

            //Icon stuff
            var icon = IconHelper.LoadApplicationIcon();

            // Set icon for any form that gets shown
            Application.ApplicationExit += (s, e) => icon?.Dispose();

        
            if (icon != null)
            {
                mainForm.Icon = icon;
                mainForm.Shown += (s, e) => IconHelper.ApplyIconToAllForms(icon);
            }



            //IconHelper.Initialize();
            Application.Run(mainForm); // Use the injected instance
        
        }

        private static IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    var connectionString = GlobalConfig.ConnectionString;
                    // var connectionString = "Server=************\\TEST;Initial Catalog=ZambiaInvoice;User Id=Zambia;Password=********;MultipleActiveResultSets=True;TrustServerCertificate=True;";
                    // Register DbContext Factory - use the built-in EF Core factory
                    services.AddDbContextFactory<AppDbContext>(options =>
                        options.UseSqlServer(connectionString));

                    // Keep one DbContext for other services that need it
                    services.AddDbContext<AppDbContext>(options =>
                        options.UseSqlServer(connectionString),
                        ServiceLifetime.Scoped);

                    // Register forms as Transient
                    services.AddTransient<frmMain>();
                    services.AddTransient<LoadingForm>();
                    services.AddTransient<ItemForm>();
                    services.AddTransient<frmCodes>();
                    services.AddTransient<frmClass>();
                    services.AddTransient<frm_AddItem>();
                    services.AddTransient<frmInvoice>();
                    services.AddTransient<frmSelectItem>();

                    // Register services
                    services.AddScoped<HeaderService>();
                    services.AddScoped<CodeService>();
                    services.AddScoped<ConfigurationService>();
                    services.AddScoped<ClassService>();
                    services.AddScoped<ItemService>();
                    services.AddScoped<ItemCodesService>();
                    services.AddScoped<ItemAddService>();
                    services.AddScoped<ApiService>(provider => new ApiService("http://localhost:8080"));
                    services.AddScoped<InvoiceService>();
                    services.AddScoped<CIS_Helper>();
                    services.AddScoped<ZRAInvoiceService>();
                });
        }
    }
}
