﻿{
   "CodeGenerationMode": 5,
   "ContextClassName": "ZambiaContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "MinimumProductVersion": "2.6.1186",
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Models\\ExistingTables",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "Zambia.Invoice",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[ZRA_INVOICE_D]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[ZRA_INVOICE_H]",
         "ObjectType": 3
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": true,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": true,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}