using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System.Threading.Tasks;
using Zambia.Invoice.Forms;
using Zambia.Invoice.Forms.ClassForms;
using Zambia.Invoice.Forms.CodesForms;
using Zambia.Invoice.Forms.InvoiceForms;
using Zambia.Invoice.Forms.ItemForms;
using Zambia.Invoice.Global;
using Zambia.Invoice.Helpers;
using Zambia.Invoice.Models;
using Zambia.Invoice.Services;

namespace Zambia.Invoice
{
    public partial class frmMain : Form
    {

        private readonly HeaderService _headerService;
        private readonly CodeService _codeService;
        private readonly ClassService _classService;
        private readonly IServiceProvider _serviceProvider;
        private readonly ZRAInvoiceService _zraInvoiceService;
        private readonly InvoiceService _invoiceService;
        private readonly ConfigurationService _configService;
        private readonly CIS_Helper _cisHelper;
        private readonly ItemService _itemService;

        private InvoiceCreateModel create = new InvoiceCreateModel();

        public frmMain(HeaderService headerService,
            CodeService codeService,
            ClassService classService,
            IServiceProvider serviceProvider,
            ConfigurationService configService,
            ZRAInvoiceService zraInvoiceService,
            InvoiceService invoiceService,
            CIS_Helper cisHelper,
            ItemService itemService)
        {
            _itemService = itemService;
            _invoiceService = invoiceService;
            _zraInvoiceService = zraInvoiceService;
            _headerService = headerService;
            _codeService = codeService;
            _classService = classService;
            _serviceProvider = serviceProvider;
            _configService = configService;

            InitializeComponent();
            dgv_pendingInvoices.CellContentClick += dgv_pendingInvoices_CellContentClick;
            _zraInvoiceService = zraInvoiceService;
            _cisHelper = cisHelper;
            dgv_CompletedInvoices.CellContentClick += dgv_CompletedInvoices_CellContentClick;
        }
        private async void frmMain_Load(object sender, EventArgs e)
        {
            //var data = await _codeService.LoadDataAsync();
            // await _codeService.LoadAndSaveDataAsync();        // Waits to complete
            //var data = _codeService.GetAllCodesFromDatabase();
            // Runs after #2

            //Copyright � 2023 Copyright � 2023 Primedia Outdoor
            lbl_copy.Text = "Copyright \u00A9 " + DateTime.Now.Year + " Primedia Outdoor";


        }

        private async void btn_ReloadCodes_Click(object sender, EventArgs e)
        {
            LoadingForm loadingForm = null;
            try
            {
                loadingForm = new LoadingForm();
                loadingForm.Show(this);

                await _codeService.DeleteAllDataWithEntitiesAsync();
                await _codeService.LoadAndSaveDataAsync();
                var count = await _codeService.GetRecordCountsAsync();  // Runs after #1
                if (count.CodesCount != 0)
                {
                    MessageBox.Show($" {count.CodesCount} Classes have been Load");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error: {ex.Message}");
            }
            finally
            {
                loadingForm?.Close();
            }

        }

        private async void btn_ReloadClasses_Click(object sender, EventArgs e)
        {
            LoadingForm loadingForm = null;
            try
            {
                loadingForm = new LoadingForm();
                loadingForm.Show(this);

                await _classService.DeleteAllDataWithEntitiesAsync();
                await _classService.LoadAndSaveDataAsync();
                var count = await _classService.GetRecordCountsAsync();  // Runs after #1
                if (count.ClassCount != 0)
                {
                    MessageBox.Show($" {count.ClassCount} Codes have been Load");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error: {ex.Message}");
            }
            finally
            {
                loadingForm?.Close();
            }


        }

        private void btn_Items_Click(object sender, EventArgs e)
        {
            var itemForm = _serviceProvider.GetRequiredService<ItemForm>();
            itemForm.Show();
        }

        private void btnVIewCodes_Click(object sender, EventArgs e)
        {
            var codesForm = _serviceProvider.GetRequiredService<frmCodes>();
            codesForm.Show();
        }

        private void btnViewClass_Click(object sender, EventArgs e)
        {
            var classForm = _serviceProvider.GetRequiredService<frmClass>();
            classForm.Show();

        }

        private void btn_Classes_Click(object sender, EventArgs e)
        {

        }

        private void btnInvoice_Click(object sender, EventArgs e)
        {
            var invoiceForm = _serviceProvider.GetRequiredService<frmInvoice>();
            invoiceForm.Show();
        }



        private async void btn_LoadPendingInvoices_Click(object sender, EventArgs e)
        {

            LoadingForm loadingForm = new LoadingForm();
            try
            {
                loadingForm.Show(this);
                var InvoiceDetails = await _zraInvoiceService.GetAllInvoiceDetailsAsync();
                var HeaderDetails = await _zraInvoiceService.GetAllInvoiceHeadersAsync();

                var InvoiceResponses = await _invoiceService.GetAllInvoicesDB_Async();

                // remove values from HeaderDetails if InvoiceResponses contains the same INVOICENO 
                var filteredHeaderDetails = HeaderDetails
                    .Where(h => !InvoiceResponses.Any(r => r.invoiceId == h.IDINVC))
                    .ToList();

                //clear dgv_pendingInvoices
                dgv_pendingInvoices.Rows.Clear();

                foreach (var item in filteredHeaderDetails)
                {
                    int invItemCount = InvoiceDetails.Count(i => i.INVOICENO == item.IDINVC);

                    //add row to data grid view dgv_pendingInvoices
                    //convert item .DATEINVC to dd-MM-yyyy format

                    dgv_pendingInvoices.Rows.Add(item.IDINVC,
                        item.IDCUST,
                        item.DATEINVC.ToDateString(),
                        invItemCount,
                        item.AMTINVCTOT,
                        item.AMTTAXTOT,
                        "Process");

                }
            }
            catch (Exception ex)
            {
                loadingForm?.Close();
                throw;
            }
            finally
            {

                loadingForm?.Close();

            }

            // now get invoices that have already been created using the CIS number from [Zambia].[ZRA].[InvoiceResponses]
            // That number will be the INVOICENO in the [Zambia].[ZRA].[ZRA_INVOICE_D] table
            // Remove them from the list 

            //Step 1: Get all existing invoice numbers from InvoiceResponses
            //Step 2: Filter out invoices from HeaderDetails and InvoiceDetails that exist in the list from Step 1
            //Step 3: Display the remaining invoices in the DataGridView


        }

        private async void btn_process_Click(object sender, EventArgs e)
        {
            // Show confirmation dialog
            var result = MessageBox.Show(
                "Are you sure you want to start processing all invoices?",
                "Confirm Processing",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
            {
                return; // Cancel if user clicked No
            }

            //make the prb_invoiceStatus visible and set its value to 0 and slowly increase to 100 with a loop
            prb_invoiceStatus.Visible = true;
            prb_invoiceStatus.Value = 0;
            for (int i = 0; i <= 100; i++)
            {
                prb_invoiceStatus.Value = i;
                System.Threading.Thread.Sleep(10);
            }
            prb_invoiceStatus.Visible = false;




        }

        private void dgv_pendingInvoices_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            // Check if the clicked cell is in the Process button column
            if (e.ColumnIndex == dgv_pendingInvoices.Columns["col_Process"].Index && e.RowIndex >= 0)
            {
                // Get the IDINVC value from the first column (col_InvoiceNum)
                var invoiceId = dgv_pendingInvoices.Rows[e.RowIndex].Cells["col_InvoiceNum"].Value?.ToString();

                if (!string.IsNullOrEmpty(invoiceId))
                {
                    StartInvoiceProcess(invoiceId);
                }
            }
        }

        private async Task StartInvoiceProcess(string invoiceId)
        {
            var result = MessageBox.Show(
               "Are you sure you want to start processing invoice " + invoiceId + " ?",
               "Confirm Processing",
               MessageBoxButtons.YesNo,
               MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
            {
                return; // Cancel if user clicked No
            }

            await ProcessInvoice(invoiceId);
        }

        private async Task ProcessInvoice(string invoiceId)
        {
            LoadingForm loadingForm = null;
            try
            {
                var items = await _itemService.GetItemList();
                loadingForm = new LoadingForm();
                loadingForm.Show(this);

                var HeaderDetails = await _zraInvoiceService.GetInvoiceHeaderByIdAsync(invoiceId);
                var InvoiceDetails = await _zraInvoiceService.GetInvoiceDetailsByIdAsync(invoiceId);

                // Generate CIS number
                string currentCisNumber = await _cisHelper.CreateCISInvoiceNumberAsync();

                // MAP HEADER DATA START
                create.itemList = new List<ItemListCreate>();
                create.cisInvcNo = currentCisNumber;
                create.bhfId = GlobalConfig.BranchCode;
                create.tpin = GlobalConfig.Tpin;
                create.salesTyCd = "N";
                create.rcptTyCd = "S";
                create.pmtTyCd = "01";
                create.salesSttsCd = "02";

                // Validate and set dates
                DateTime invoiceDate = DateTime.ParseExact(HeaderDetails.DATEINVC, "yyyyMMdd", null);
                DateTime today = DateTime.Now.Date;
                TimeSpan difference = today - invoiceDate;

                if (Math.Abs(difference.TotalDays) > 30)
                {
                    MessageBox.Show(
                        $"Invoice date ({invoiceDate:yyyy-MM-dd}) is {Math.Abs(difference.TotalDays):F0} days old.\n\n" +
                        "ZRA API only accepts invoices within 30 days of the invoice date.\n\n" +
                        "Today's date will be used instead.",
                        "Date Adjusted",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );

                    create.salesDt = today.ToString("yyyyMMdd");
                    create.cfmDt = DateTime.Now.ToString("yyyyMMddHHmmss");
                }
                else
                {
                    create.salesDt = HeaderDetails.DATEINVC.ToDateString(true);
                    create.cfmDt = HeaderDetails.DATEINVC.ToDateString(true, true);
                }

                // Customer details
                create.custTpin = null;
                create.custNm = HeaderDetails.IDCUST;

                // Stock and cancellation dates
                create.stockRlsDt = null;
                create.cnclDt = "20000101000000";
                create.cnclReqDt = "20000101000000";
                create.rfdDt = "20000101000000";
                create.lpoNumber = null;

                // Required fields
                create.rfdRsnCd = null;
                create.saleCtyCd = "1";
                create.orgInvcNo = 0;

                // Optional fields
                create.destnCountryCd = "";
                create.dbtRsnCd = "";
                create.invcAdjustReason = "";
                create.remark = "";

                create.totItemCnt = InvoiceDetails.Count();

                // Calculate totals from line items
                double totAmtValue = 0;
                double totTaxblAmt = 0;
                double totTaxAmt = 0;

                foreach (var detail in InvoiceDetails)
                {
                    double lineTotal = (double)(detail.QTYINVC * detail.AMTPRIC);
                    double lineTaxExclusive = lineTotal / 1.16;
                    double lineTaxAmount = lineTaxExclusive * 0.16;

                    totAmtValue += lineTotal;
                    totTaxblAmt += lineTaxExclusive;
                    totTaxAmt += lineTaxAmount;
                }

                // Set calculated header totals with proper rounding
                create.totAmt = Math.Round(totAmtValue, 2);
                create.totTaxblAmt = Math.Round(totTaxblAmt, 2);
                create.totTaxAmt = Math.Round(totTaxAmt, 2);

                // User details
                create.regrId = "system";
                create.regrNm = "system";
                create.modrId = "system";
                create.modrNm = "system";

                // Currency
                create.currencyTyCd = "ZMW";
                create.exchangeRt = "1";
                create.prchrAcptcYn = "N";

                // Tax rates
                create.taxRtA = 16;
                create.taxRtB = 16;
                create.taxRtC1 = 0;
                create.taxRtC2 = 0;
                create.taxRtC3 = 0;
                create.taxRtD = 0;
                create.taxRtRvat = 16;
                create.taxRtE = 0;
                create.taxRtF = 10;
                create.taxRtIpl1 = 5;
                create.taxRtIpl2 = 0;
                create.taxRtTl = 1.5;
                create.taxRtEcm = 5;
                create.taxRtExeeg = 3;
                create.taxRtTot = 0;

                // Tax amounts by category
                create.taxAmtA = Math.Round(totTaxAmt, 4);
                create.taxAmtB = 0;
                create.taxAmtC1 = 0;
                create.taxAmtC2 = 0;
                create.taxAmtC3 = 0;
                create.taxAmtD = 0;
                create.taxAmtRvat = 0;
                create.taxAmtE = 0;
                create.taxAmtF = 0;
                create.taxAmtIpl1 = 0;
                create.taxAmtIpl2 = 0;
                create.taxAmtTl = 0;
                create.taxAmtEcm = 0;
                create.taxAmtExeeg = 0;
                create.taxAmtTot = 0;

                // Taxable amounts by category
                create.taxblAmtA = Math.Round(totTaxblAmt, 4);
                create.taxblAmtB = 0;
                create.taxblAmtC1 = 0;
                create.taxblAmtC2 = 0;
                create.taxblAmtC3 = 0;
                create.taxblAmtD = 0;
                create.taxblAmtRvat = 0;
                create.taxblAmtE = 0;
                create.taxblAmtF = 0;
                create.taxblAmtIpl1 = 0;
                create.taxblAmtIpl2 = 0;
                create.taxblAmtTl = 0;
                create.taxblAmtEcm = 0;
                create.taxblAmtExeeg = 0;
                create.taxblAmtTot = 0;
                create.cashDcRt = 0;
                create.cashDcAmt = 0;

                // MAP HEADER DATA END

                // Map InvoiceDetails to itemList
                int seqNo = 1;
                foreach (var detail in InvoiceDetails)
                {
                    double lineTotal = (double)(detail.QTYINVC * detail.AMTPRIC);
                    double vatTaxblAmt = lineTotal / 1.16;
                    double vatAmt = vatTaxblAmt * 0.16;

                    var matchedItem = items.FirstOrDefault(s => s.itemNm == detail.IDITEM);

                    if (matchedItem == null)
                    {
                        throw new Exception($"Item '{detail.IDITEM}' not found in registered items list");
                    }

                    create.itemList.Add(new ItemListCreate
                    {
                        itemSeq = seqNo++,
                        itemCd = matchedItem.itemCd,
                        itemClsCd = matchedItem.itemClsCd,
                        itemNm = detail.TEXTDESC,
                        bcd = "",
                        pkgUnitCd = "EA",
                        pkg = 0.0,
                        qtyUnitCd = "EA",
                        qty = detail.QTYINVC,
                        prc = (double)detail.AMTPRIC,
                        splyAmt = Math.Round(lineTotal, 4),
                        dcRt = 0.0,
                        dcAmt = 0.0,
                        isrccCd = "",
                        isrccNm = "",
                        isrcRt = 0.0,
                        isrcAmt = 0.0,
                        vatCatCd = "A",
                        exciseTxCatCd = "",
                        vatTaxblAmt = Math.Round(vatTaxblAmt, 4),
                        exciseTaxblAmt = 0.0,
                        tlTaxblAmt = 0.0,
                        iplTaxblAmt = 0.0,
                        iplAmt = 0.0,
                        tlAmt = 0.0,
                        vatAmt = Math.Round(vatAmt, 4),
                        exciseTxAmt = 0.0,
                        totAmt = Math.Round(lineTotal, 2)
                    });
                }

                string json = JsonConvert.SerializeObject(create, Formatting.Indented);

                var response = await _invoiceService.SaveInvoiceAPI_Async(create);

                if (response != null && response.resultCd == "000")
                {
                    await _cisHelper.SaveSuccessfulInvoiceDB_Async(invoiceId, currentCisNumber, response);
                    MessageBox.Show($"Invoice {invoiceId} processed successfully: {response.resultMsg}");
                }
                else
                {
                    await _cisHelper.RemoveCISInvoiceNumberAsync(currentCisNumber);
                    MessageBox.Show($"Invoice {invoiceId} processing failed: {response?.resultMsg ?? "Unknown error"}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error processing invoice {invoiceId}: {ex.Message}");
            }
            finally
            {
                loadingForm?.Close();
            }
        }

        private async void btn_RefreshCompleted_Click(object sender, EventArgs e)
        {
            try
            {
                var invoiceResponses = await _invoiceService.GetAllCompletedInvoicesDB_Async();
                // populate the dgv_CompletedInvoices
                dgv_CompletedInvoices.Rows.Clear();
                foreach (var response in invoiceResponses)
                {
                    int rowIndex = dgv_CompletedInvoices.Rows.Add(response.CreatedAt,
                        response.rcptNo,
                        response.mrcNo,
                        response.CIS,
                        response.qrCodeUrl, 
                        "View QR"
                      
                        );
                    dgv_CompletedInvoices.Rows[rowIndex].Tag = response.qrCodeImage;
                }

            }
            catch (Exception)
            {

                throw;
            }
            finally { 
            
            
            }
        }

        private void dgv_CompletedInvoices_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex == 4) // Column index for "View QR Link" 
            {
                var qrCodeUrl = dgv_CompletedInvoices.Rows[e.RowIndex].Cells[4].Value?.ToString(); // qrCodeUrl column
                
                if (!string.IsNullOrEmpty(qrCodeUrl))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = qrCodeUrl,
                            UseShellExecute = true
                        });
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error opening URL: {ex.Message}");
                    }
                }
            }
            if (e.RowIndex >= 0 && e.ColumnIndex == 5) // view QR code Image
            {
                // Get the qrCodeImage from the row's Tag
                var base64Image = dgv_CompletedInvoices.Rows[e.RowIndex].Tag?.ToString();

                if (!string.IsNullOrEmpty(base64Image))
                {
                    ShowQRCodeImage(base64Image);
                }
                else
                {
                    MessageBox.Show("No QR code available", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }
        private void ShowQRCodeImage(string dataUri)
        {
            // Remove the data URI prefix to get just the base64 string
            string base64String = dataUri.Replace("data:image/png;base64,", "");

            byte[] imageBytes = Convert.FromBase64String(base64String);

            using var ms = new MemoryStream(imageBytes);
            using var image = Image.FromStream(ms);

            var imageForm = new Form
            {
                Text = "QR Code",
                Width = 400,
                Height = 400,
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var pictureBox = new PictureBox
            {
                Image = (Image)image.Clone(),
                SizeMode = PictureBoxSizeMode.Zoom,
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };

            imageForm.Controls.Add(pictureBox);
            imageForm.ShowDialog();
        }
    }
}
