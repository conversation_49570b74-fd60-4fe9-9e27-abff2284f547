﻿SELECT TOP (1000) i.[Id]
      ,i.[tpin]
      ,i.[itemCd]
      ,i.[itemClsCd]
      ,i.[itemTyCd]
      ,i.[itemNm]
      ,i.[itemStdNm]
      ,i.[orgnNatCd]
      ,i.[pkgUnitCd]
      ,i.[qtyUnitCd]
      ,i.[vatCatCd]
      ,i.[iplCatCd]
      ,i.[tlCatCd]
      ,i.[exciseTxCatCd]
      ,i.[btchNo]
      ,i.[regBhfId]
      ,i.[bcd]
      ,i.[dftPrc]
      ,i.[addInfo]
      ,i.[sftyQty]
      ,i.[ItemResponseEntityId],
	  r.[Id]
      ,r.[resultCd]
      ,r.[resultMsg]
      ,r.[resultDt]
  FROM [ZambiaInvoice].[Item].[ItemLists] i inner join [ZambiaInvoice].[Item].[ItemResponses] r on i.ItemResponseEntityId = r.Id