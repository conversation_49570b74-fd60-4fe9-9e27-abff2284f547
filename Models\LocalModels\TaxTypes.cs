﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Models.LocalModels
{
    public static class VATTaxTypes
    {
        public static List<DropDownValueText> VATList = new List<DropDownValueText>
    {
        new DropDownValueText { Value = "A", Text = "Standard Rated" },
        new DropDownValueText { Value = "B", Text = "Minimum Taxable Value (MTV)" },
        new DropDownValueText { Value = "C1", Text = "Exports" },
        new DropDownValueText { Value = "C2", Text = "Zero-rating Local Purchases Order transactions" },
        new DropDownValueText { Value = "C3", Text = "Zero-rated by nature" },
        new DropDownValueText { Value = "D", Text = "Exempt" },
        new DropDownValueText { Value = "E", Text = "Disbursement" },
        new DropDownValueText { Value = "RVAT", Text = "Reverse VAT" }
    };
    }

    public static class InsurancePremiumLevyTaxTypes
    {
        public static List<DropDownValueText> IPLList = new List<DropDownValueText>
    {
        new DropDownValueText { Value = "IPL1", Text = "Insurance Premium Levy" },
        new DropDownValueText { Value = "IPL2", Text = "Re-Insurance" }
    };
    }

    public static class TourismLevyTaxTypes
    {
        public static List<DropDownValueText> TLList = new List<DropDownValueText>
    {
        new DropDownValueText { Value = "TL", Text = "Tourism Levy" },
        new DropDownValueText { Value = "F", Text = "Service Charge" }
    };
    }

    public static class ExciseTaxTypes
    {
        public static List<DropDownValueText> ExciseList = new List<DropDownValueText>
    {
        new DropDownValueText { Value = "ECM", Text = "Excise on Coal" },
        new DropDownValueText { Value = "EXEEG", Text = "Excise Electricity" }
    };
    }

    public static class TurnoverTaxTypes
    {
        public static List<DropDownValueText> TOTList = new List<DropDownValueText>
    {
        new DropDownValueText { Value = "TOT", Text = "Turnover Tax" }
    };
    }
}
