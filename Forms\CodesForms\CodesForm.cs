﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Zambia.Invoice.Services;
using Zambia.Invoice.Models;

namespace Zambia.Invoice.Forms.CodesForms
{
    public partial class frmCodes : Form
    {
        private readonly CodeService _codeService;
        private List<DtlList> _allCodes = new List<DtlList>();

        public frmCodes(CodeService codeService)
        {
            InitializeComponent();
            _codeService = codeService;
        }

        private async void frmCodes_Load(object sender, EventArgs e)
        {
            await LoadCodesData();
            txtFilter.TextChanged += TxtFilter_TextChanged;
        }

        private async Task LoadCodesData()
        {
            try
            {
                var codesData = await _codeService.GetAllCodesFromDatabase();

                // Flatten the hierarchical data to show all DtlList items
                _allCodes = codesData
                    .SelectMany(c => c.ClsList)
                    .SelectMany(cl => cl.dtlList)
                    .ToList();

                dataGridView.DataSource = _allCodes;

                if (dataGridView.Columns.Count > 0)
                {
                    dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading codes data: {ex.Message}");
            }
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            string filterText = txtFilter.Text.Trim().ToLower();

            if (string.IsNullOrEmpty(filterText))
            {
                dataGridView.DataSource = _allCodes;
            }
            else
            {
                var filtered = _allCodes
                    .Where(code =>
                        (!string.IsNullOrEmpty(code.cd) && code.cd.ToLower().Contains(filterText)) ||
                        (!string.IsNullOrEmpty(code.cdNm) && code.cdNm.ToLower().Contains(filterText)) ||
                        (!string.IsNullOrEmpty(code.userDfnCd1) && code.userDfnCd1.ToLower().Contains(filterText))
                    ).ToList();

                dataGridView.DataSource = filtered;
            }

            dataGridView.Refresh();
        }

   
    }
}
