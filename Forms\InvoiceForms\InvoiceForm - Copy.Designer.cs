﻿namespace Zambia.Invoice.Forms.InvoiceForms2
{
    partial class frmInvoice2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            grbRequired = new GroupBox();
            cmb_pmtTyCd = new ComboBox();
            dtp_salesDt = new DateTimePicker();
            cmb_salesSttsCd = new ComboBox();
            cmb_rcptTyCd = new ComboBox();
            cmb_salesTyCd = new ComboBox();
            dtp_cfmDt = new DateTimePicker();
            txt_regrNm = new TextBox();
            label33 = new Label();
            txt_exchangeRt = new TextBox();
            label13 = new Label();
            label14 = new Label();
            txt_currencyTyCd = new TextBox();
            txt_modrNm = new TextBox();
            label15 = new Label();
            label16 = new Label();
            txt_modrId = new TextBox();
            txt_regrId = new TextBox();
            label9 = new Label();
            label10 = new Label();
            txt_totAmt = new TextBox();
            txt_totTaxAmt = new TextBox();
            label11 = new Label();
            label12 = new Label();
            txt_totTaxblAmt = new TextBox();
            txt_totItemCnt = new TextBox();
            label5 = new Label();
            label6 = new Label();
            label7 = new Label();
            label8 = new Label();
            label3 = new Label();
            label4 = new Label();
            txt_bhfId = new TextBox();
            txt_tpin = new TextBox();
            lbl_bhfId = new Label();
            lbl_tpin = new Label();
            txt_cisInvcNo = new TextBox();
            label31 = new Label();
            label28 = new Label();
            grbOptional = new GroupBox();
            btn_CreateInvoice = new Button();
            cmb_prchrAcptcYn = new ComboBox();
            dtp_cnclDt = new DateTimePicker();
            dtp_cnclReqDt = new DateTimePicker();
            dtp_stockRlsDt = new DateTimePicker();
            dtp_rfdDt = new DateTimePicker();
            txt_dbtRsnCd = new TextBox();
            txt_destnCountryCd = new TextBox();
            label34 = new Label();
            label35 = new Label();
            txt_lpoNumber = new TextBox();
            txt_saleCtyCd = new TextBox();
            label36 = new Label();
            label37 = new Label();
            sel_remark = new TextBox();
            label38 = new Label();
            label39 = new Label();
            txt_cashDcAmt = new TextBox();
            txt_cashDcRt = new TextBox();
            label17 = new Label();
            label18 = new Label();
            textBox19 = new TextBox();
            textBox20 = new TextBox();
            label19 = new Label();
            label20 = new Label();
            textBox21 = new TextBox();
            txt_invcAdjustReason = new TextBox();
            label21 = new Label();
            label22 = new Label();
            txt_rfdRsnCd = new TextBox();
            label23 = new Label();
            label24 = new Label();
            label25 = new Label();
            label26 = new Label();
            label27 = new Label();
            txt_custNm = new TextBox();
            txt_custTpin = new TextBox();
            label29 = new Label();
            label30 = new Label();
            txt_orgIncNo = new TextBox();
            label32 = new Label();
            lbl_ItemTitle = new Label();
            dgv_Items = new DataGridView();
            itemNm = new DataGridViewTextBoxColumn();
            itemCd = new DataGridViewTextBoxColumn();
            qtyUnitCd = new DataGridViewTextBoxColumn();
            prc = new DataGridViewTextBoxColumn();
            dcRt = new DataGridViewTextBoxColumn();
            pkg = new DataGridViewTextBoxColumn();
            totAmt = new DataGridViewTextBoxColumn();
            splyAmt = new DataGridViewTextBoxColumn();
            itemClsCd = new DataGridViewTextBoxColumn();
            btn_AddItem = new Button();
            grbRequired.SuspendLayout();
            grbOptional.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgv_Items).BeginInit();
            SuspendLayout();
            // 
            // grbRequired
            // 
            grbRequired.Controls.Add(cmb_pmtTyCd);
            grbRequired.Controls.Add(dtp_salesDt);
            grbRequired.Controls.Add(cmb_salesSttsCd);
            grbRequired.Controls.Add(cmb_rcptTyCd);
            grbRequired.Controls.Add(cmb_salesTyCd);
            grbRequired.Controls.Add(dtp_cfmDt);
            grbRequired.Controls.Add(txt_regrNm);
            grbRequired.Controls.Add(label33);
            grbRequired.Controls.Add(txt_exchangeRt);
            grbRequired.Controls.Add(label13);
            grbRequired.Controls.Add(label14);
            grbRequired.Controls.Add(txt_currencyTyCd);
            grbRequired.Controls.Add(txt_modrNm);
            grbRequired.Controls.Add(label15);
            grbRequired.Controls.Add(label16);
            grbRequired.Controls.Add(txt_modrId);
            grbRequired.Controls.Add(txt_regrId);
            grbRequired.Controls.Add(label9);
            grbRequired.Controls.Add(label10);
            grbRequired.Controls.Add(txt_totAmt);
            grbRequired.Controls.Add(txt_totTaxAmt);
            grbRequired.Controls.Add(label11);
            grbRequired.Controls.Add(label12);
            grbRequired.Controls.Add(txt_totTaxblAmt);
            grbRequired.Controls.Add(txt_totItemCnt);
            grbRequired.Controls.Add(label5);
            grbRequired.Controls.Add(label6);
            grbRequired.Controls.Add(label7);
            grbRequired.Controls.Add(label8);
            grbRequired.Controls.Add(label3);
            grbRequired.Controls.Add(label4);
            grbRequired.Controls.Add(txt_bhfId);
            grbRequired.Controls.Add(txt_tpin);
            grbRequired.Controls.Add(lbl_bhfId);
            grbRequired.Controls.Add(lbl_tpin);
            grbRequired.Controls.Add(txt_cisInvcNo);
            grbRequired.Controls.Add(label31);
            grbRequired.Controls.Add(label28);
            grbRequired.Location = new Point(16, 16);
            grbRequired.Name = "grbRequired";
            grbRequired.Size = new Size(564, 573);
            grbRequired.TabIndex = 0;
            grbRequired.TabStop = false;
            grbRequired.Text = "Required";
            // 
            // cmb_pmtTyCd
            // 
            cmb_pmtTyCd.FormattingEnabled = true;
            cmb_pmtTyCd.Items.AddRange(new object[] { "Cash", "Card" });
            cmb_pmtTyCd.Location = new Point(143, 176);
            cmb_pmtTyCd.Name = "cmb_pmtTyCd";
            cmb_pmtTyCd.Size = new Size(148, 23);
            cmb_pmtTyCd.TabIndex = 38;
            // 
            // dtp_salesDt
            // 
            dtp_salesDt.Location = new Point(143, 234);
            dtp_salesDt.Name = "dtp_salesDt";
            dtp_salesDt.Size = new Size(148, 23);
            dtp_salesDt.TabIndex = 37;
            // 
            // cmb_salesSttsCd
            // 
            cmb_salesSttsCd.FormattingEnabled = true;
            cmb_salesSttsCd.Items.AddRange(new object[] { "Pending", "Confirmed" });
            cmb_salesSttsCd.Location = new Point(143, 205);
            cmb_salesSttsCd.Name = "cmb_salesSttsCd";
            cmb_salesSttsCd.Size = new Size(148, 23);
            cmb_salesSttsCd.TabIndex = 36;
            // 
            // cmb_rcptTyCd
            // 
            cmb_rcptTyCd.FormattingEnabled = true;
            cmb_rcptTyCd.Items.AddRange(new object[] { "Sales", "Proforma" });
            cmb_rcptTyCd.Location = new Point(143, 147);
            cmb_rcptTyCd.Name = "cmb_rcptTyCd";
            cmb_rcptTyCd.Size = new Size(148, 23);
            cmb_rcptTyCd.TabIndex = 35;
            // 
            // cmb_salesTyCd
            // 
            cmb_salesTyCd.FormattingEnabled = true;
            cmb_salesTyCd.Items.AddRange(new object[] { "Normal", "Return" });
            cmb_salesTyCd.Location = new Point(143, 118);
            cmb_salesTyCd.Name = "cmb_salesTyCd";
            cmb_salesTyCd.Size = new Size(148, 23);
            cmb_salesTyCd.TabIndex = 34;
            cmb_salesTyCd.SelectedIndexChanged += cmb_salesTyCd_SelectedIndexChanged;
            // 
            // dtp_cfmDt
            // 
            dtp_cfmDt.Location = new Point(407, 28);
            dtp_cfmDt.Name = "dtp_cfmDt";
            dtp_cfmDt.Size = new Size(151, 23);
            dtp_cfmDt.TabIndex = 45;
            // 
            // txt_regrNm
            // 
            txt_regrNm.Location = new Point(143, 408);
            txt_regrNm.Name = "txt_regrNm";
            txt_regrNm.Size = new Size(148, 23);
            txt_regrNm.TabIndex = 33;
            // 
            // label33
            // 
            label33.AutoSize = true;
            label33.Location = new Point(23, 416);
            label33.Name = "label33";
            label33.Size = new Size(88, 15);
            label33.TabIndex = 32;
            label33.Text = "Registrar Name";
            // 
            // txt_exchangeRt
            // 
            txt_exchangeRt.Location = new Point(143, 524);
            txt_exchangeRt.Name = "txt_exchangeRt";
            txt_exchangeRt.Size = new Size(148, 23);
            txt_exchangeRt.TabIndex = 30;
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.Location = new Point(23, 184);
            label13.Name = "label13";
            label13.Size = new Size(112, 15);
            label13.TabIndex = 29;
            label13.Text = "Payment Type Code";
            // 
            // label14
            // 
            label14.AutoSize = true;
            label14.Location = new Point(23, 532);
            label14.Name = "label14";
            label14.Size = new Size(84, 15);
            label14.TabIndex = 28;
            label14.Text = "Exchange Rate";
            // 
            // txt_currencyTyCd
            // 
            txt_currencyTyCd.Location = new Point(143, 495);
            txt_currencyTyCd.Name = "txt_currencyTyCd";
            txt_currencyTyCd.Size = new Size(148, 23);
            txt_currencyTyCd.TabIndex = 27;
            // 
            // txt_modrNm
            // 
            txt_modrNm.Location = new Point(143, 466);
            txt_modrNm.Name = "txt_modrNm";
            txt_modrNm.Size = new Size(148, 23);
            txt_modrNm.TabIndex = 26;
            // 
            // label15
            // 
            label15.AutoSize = true;
            label15.Location = new Point(23, 503);
            label15.Name = "label15";
            label15.Size = new Size(113, 15);
            label15.TabIndex = 25;
            label15.Text = "Currency Type Code";
            // 
            // label16
            // 
            label16.AutoSize = true;
            label16.Location = new Point(23, 474);
            label16.Name = "label16";
            label16.Size = new Size(87, 15);
            label16.TabIndex = 24;
            label16.Text = "Modifier Name";
            // 
            // txt_modrId
            // 
            txt_modrId.Location = new Point(143, 437);
            txt_modrId.Name = "txt_modrId";
            txt_modrId.Size = new Size(148, 23);
            txt_modrId.TabIndex = 23;
            // 
            // txt_regrId
            // 
            txt_regrId.Location = new Point(143, 379);
            txt_regrId.Name = "txt_regrId";
            txt_regrId.Size = new Size(148, 23);
            txt_regrId.TabIndex = 22;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new Point(23, 445);
            label9.Name = "label9";
            label9.Size = new Size(66, 15);
            label9.TabIndex = 21;
            label9.Text = "Modifier ID";
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new Point(23, 387);
            label10.Name = "label10";
            label10.Size = new Size(73, 15);
            label10.TabIndex = 20;
            label10.Text = "Registrant Id";
            // 
            // txt_totAmt
            // 
            txt_totAmt.Location = new Point(143, 350);
            txt_totAmt.Name = "txt_totAmt";
            txt_totAmt.Size = new Size(148, 23);
            txt_totAmt.TabIndex = 19;
            // 
            // txt_totTaxAmt
            // 
            txt_totTaxAmt.Location = new Point(143, 321);
            txt_totTaxAmt.Name = "txt_totTaxAmt";
            txt_totTaxAmt.Size = new Size(148, 23);
            txt_totTaxAmt.TabIndex = 18;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new Point(23, 358);
            label11.Name = "label11";
            label11.Size = new Size(79, 15);
            label11.TabIndex = 17;
            label11.Text = "Total Amount";
            label11.Click += label11_Click;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new Point(23, 329);
            label12.Name = "label12";
            label12.Size = new Size(99, 15);
            label12.TabIndex = 16;
            label12.Text = "Total Tax Amount";
            // 
            // txt_totTaxblAmt
            // 
            txt_totTaxblAmt.Location = new Point(143, 292);
            txt_totTaxblAmt.Name = "txt_totTaxblAmt";
            txt_totTaxblAmt.Size = new Size(148, 23);
            txt_totTaxblAmt.TabIndex = 15;
            // 
            // txt_totItemCnt
            // 
            txt_totItemCnt.Location = new Point(143, 263);
            txt_totItemCnt.Name = "txt_totItemCnt";
            txt_totItemCnt.Size = new Size(148, 23);
            txt_totItemCnt.TabIndex = 14;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(23, 300);
            label5.Name = "label5";
            label5.Size = new Size(121, 15);
            label5.TabIndex = 13;
            label5.Text = "Total Taxable Amount";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(23, 271);
            label6.Name = "label6";
            label6.Size = new Size(95, 15);
            label6.TabIndex = 12;
            label6.Text = "Total Item Count";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new Point(23, 242);
            label7.Name = "label7";
            label7.Size = new Size(60, 15);
            label7.TabIndex = 9;
            label7.Text = "Sales Date";
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new Point(23, 213);
            label8.Name = "label8";
            label8.Size = new Size(99, 15);
            label8.TabIndex = 8;
            label8.Text = "Sales Status Code";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(23, 155);
            label3.Name = "label3";
            label3.Size = new Size(104, 15);
            label3.TabIndex = 5;
            label3.Text = "Receipt Type Code";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(23, 126);
            label4.Name = "label4";
            label4.Size = new Size(91, 15);
            label4.TabIndex = 4;
            label4.Text = "Sales Type Code";
            // 
            // txt_bhfId
            // 
            txt_bhfId.Location = new Point(143, 89);
            txt_bhfId.MaxLength = 3;
            txt_bhfId.Name = "txt_bhfId";
            txt_bhfId.Size = new Size(148, 23);
            txt_bhfId.TabIndex = 3;
            txt_bhfId.Text = "000";
            // 
            // txt_tpin
            // 
            txt_tpin.Location = new Point(143, 28);
            txt_tpin.MaxLength = 10;
            txt_tpin.Name = "txt_tpin";
            txt_tpin.Size = new Size(148, 23);
            txt_tpin.TabIndex = 2;
            txt_tpin.Text = "1002087869";
            // 
            // lbl_bhfId
            // 
            lbl_bhfId.AutoSize = true;
            lbl_bhfId.Location = new Point(23, 97);
            lbl_bhfId.Name = "lbl_bhfId";
            lbl_bhfId.Size = new Size(58, 15);
            lbl_bhfId.TabIndex = 1;
            lbl_bhfId.Text = "Branch ID";
            // 
            // lbl_tpin
            // 
            lbl_tpin.AutoSize = true;
            lbl_tpin.Location = new Point(23, 37);
            lbl_tpin.Name = "lbl_tpin";
            lbl_tpin.Size = new Size(63, 15);
            lbl_tpin.TabIndex = 0;
            lbl_tpin.Text = "Seller TPIN";
            // 
            // txt_cisInvcNo
            // 
            txt_cisInvcNo.Location = new Point(143, 57);
            txt_cisInvcNo.Name = "txt_cisInvcNo";
            txt_cisInvcNo.Size = new Size(148, 23);
            txt_cisInvcNo.TabIndex = 3;
            // 
            // label31
            // 
            label31.AutoSize = true;
            label31.Location = new Point(23, 65);
            label31.Name = "label31";
            label31.Size = new Size(84, 15);
            label31.TabIndex = 1;
            label31.Text = "CIS Invoice No";
            // 
            // label28
            // 
            label28.AutoSize = true;
            label28.Location = new Point(297, 31);
            label28.Name = "label28";
            label28.Size = new Size(104, 15);
            label28.TabIndex = 8;
            label28.Text = "Confirm DateTime";
            // 
            // grbOptional
            // 
            grbOptional.Controls.Add(btn_CreateInvoice);
            grbOptional.Controls.Add(cmb_prchrAcptcYn);
            grbOptional.Controls.Add(dtp_cnclDt);
            grbOptional.Controls.Add(dtp_cnclReqDt);
            grbOptional.Controls.Add(dtp_stockRlsDt);
            grbOptional.Controls.Add(dtp_rfdDt);
            grbOptional.Controls.Add(txt_dbtRsnCd);
            grbOptional.Controls.Add(txt_destnCountryCd);
            grbOptional.Controls.Add(label34);
            grbOptional.Controls.Add(label35);
            grbOptional.Controls.Add(txt_lpoNumber);
            grbOptional.Controls.Add(txt_saleCtyCd);
            grbOptional.Controls.Add(label36);
            grbOptional.Controls.Add(label37);
            grbOptional.Controls.Add(sel_remark);
            grbOptional.Controls.Add(label38);
            grbOptional.Controls.Add(label39);
            grbOptional.Controls.Add(txt_cashDcAmt);
            grbOptional.Controls.Add(txt_cashDcRt);
            grbOptional.Controls.Add(label17);
            grbOptional.Controls.Add(label18);
            grbOptional.Controls.Add(textBox19);
            grbOptional.Controls.Add(textBox20);
            grbOptional.Controls.Add(label19);
            grbOptional.Controls.Add(label20);
            grbOptional.Controls.Add(textBox21);
            grbOptional.Controls.Add(txt_invcAdjustReason);
            grbOptional.Controls.Add(label21);
            grbOptional.Controls.Add(label22);
            grbOptional.Controls.Add(txt_rfdRsnCd);
            grbOptional.Controls.Add(label23);
            grbOptional.Controls.Add(label24);
            grbOptional.Controls.Add(label25);
            grbOptional.Controls.Add(label26);
            grbOptional.Controls.Add(label27);
            grbOptional.Controls.Add(txt_custNm);
            grbOptional.Controls.Add(txt_custTpin);
            grbOptional.Controls.Add(label29);
            grbOptional.Controls.Add(label30);
            grbOptional.Controls.Add(txt_orgIncNo);
            grbOptional.Controls.Add(label32);
            grbOptional.Location = new Point(586, 16);
            grbOptional.Name = "grbOptional";
            grbOptional.Size = new Size(610, 573);
            grbOptional.TabIndex = 1;
            grbOptional.TabStop = false;
            grbOptional.Text = "Optional";
            grbOptional.Visible = false;
            grbOptional.Enter += grbOptional_Enter;
            // 
            // btn_CreateInvoice
            // 
            btn_CreateInvoice.Location = new Point(438, 539);
            btn_CreateInvoice.Name = "btn_CreateInvoice";
            btn_CreateInvoice.Size = new Size(116, 23);
            btn_CreateInvoice.TabIndex = 50;
            btn_CreateInvoice.Text = "Create Invoice";
            btn_CreateInvoice.UseVisualStyleBackColor = true;
            btn_CreateInvoice.Click += btn_CreateInvoice_Click;
            // 
            // cmb_prchrAcptcYn
            // 
            cmb_prchrAcptcYn.FormattingEnabled = true;
            cmb_prchrAcptcYn.Items.AddRange(new object[] { "Y", "N" });
            cmb_prchrAcptcYn.Location = new Point(170, 451);
            cmb_prchrAcptcYn.Name = "cmb_prchrAcptcYn";
            cmb_prchrAcptcYn.Size = new Size(148, 23);
            cmb_prchrAcptcYn.TabIndex = 49;
            // 
            // dtp_cnclDt
            // 
            dtp_cnclDt.Location = new Point(170, 186);
            dtp_cnclDt.Name = "dtp_cnclDt";
            dtp_cnclDt.Size = new Size(148, 23);
            dtp_cnclDt.TabIndex = 48;
            // 
            // dtp_cnclReqDt
            // 
            dtp_cnclReqDt.Location = new Point(170, 157);
            dtp_cnclReqDt.Name = "dtp_cnclReqDt";
            dtp_cnclReqDt.Size = new Size(148, 23);
            dtp_cnclReqDt.TabIndex = 47;
            dtp_cnclReqDt.ValueChanged += dateTimePicker4_ValueChanged;
            // 
            // dtp_stockRlsDt
            // 
            dtp_stockRlsDt.Location = new Point(170, 128);
            dtp_stockRlsDt.Name = "dtp_stockRlsDt";
            dtp_stockRlsDt.Size = new Size(148, 23);
            dtp_stockRlsDt.TabIndex = 46;
            // 
            // dtp_rfdDt
            // 
            dtp_rfdDt.Location = new Point(170, 213);
            dtp_rfdDt.Name = "dtp_rfdDt";
            dtp_rfdDt.Size = new Size(148, 23);
            dtp_rfdDt.TabIndex = 44;
            // 
            // txt_dbtRsnCd
            // 
            txt_dbtRsnCd.Location = new Point(170, 544);
            txt_dbtRsnCd.Name = "txt_dbtRsnCd";
            txt_dbtRsnCd.Size = new Size(148, 23);
            txt_dbtRsnCd.TabIndex = 43;
            // 
            // txt_destnCountryCd
            // 
            txt_destnCountryCd.Location = new Point(170, 515);
            txt_destnCountryCd.Name = "txt_destnCountryCd";
            txt_destnCountryCd.Size = new Size(148, 23);
            txt_destnCountryCd.TabIndex = 42;
            // 
            // label34
            // 
            label34.AutoSize = true;
            label34.Location = new Point(13, 547);
            label34.Name = "label34";
            label34.Size = new Size(107, 15);
            label34.TabIndex = 41;
            label34.Text = "Debit Reason Code";
            // 
            // label35
            // 
            label35.AutoSize = true;
            label35.Location = new Point(13, 518);
            label35.Name = "label35";
            label35.Size = new Size(144, 15);
            label35.TabIndex = 40;
            label35.Text = "Destination Country Code";
            // 
            // txt_lpoNumber
            // 
            txt_lpoNumber.Location = new Point(438, 57);
            txt_lpoNumber.Name = "txt_lpoNumber";
            txt_lpoNumber.Size = new Size(148, 23);
            txt_lpoNumber.TabIndex = 39;
            // 
            // txt_saleCtyCd
            // 
            txt_saleCtyCd.Location = new Point(438, 28);
            txt_saleCtyCd.Name = "txt_saleCtyCd";
            txt_saleCtyCd.Size = new Size(148, 23);
            txt_saleCtyCd.TabIndex = 38;
            // 
            // label36
            // 
            label36.AutoSize = true;
            label36.Location = new Point(341, 63);
            label36.Name = "label36";
            label36.Size = new Size(76, 15);
            label36.TabIndex = 37;
            label36.Text = "LPO Number";
            // 
            // label37
            // 
            label37.AutoSize = true;
            label37.Location = new Point(341, 34);
            label37.Name = "label37";
            label37.Size = new Size(83, 15);
            label37.TabIndex = 36;
            label37.Text = "Sale City Code";
            // 
            // sel_remark
            // 
            sel_remark.Location = new Point(170, 480);
            sel_remark.Name = "sel_remark";
            sel_remark.Size = new Size(148, 23);
            sel_remark.TabIndex = 35;
            // 
            // label38
            // 
            label38.AutoSize = true;
            label38.Location = new Point(13, 488);
            label38.Name = "label38";
            label38.Size = new Size(47, 15);
            label38.TabIndex = 33;
            label38.Text = "Remark";
            // 
            // label39
            // 
            label39.AutoSize = true;
            label39.Location = new Point(13, 459);
            label39.Name = "label39";
            label39.Size = new Size(124, 15);
            label39.TabIndex = 32;
            label39.Text = "Purchaser Acceptance";
            // 
            // txt_cashDcAmt
            // 
            txt_cashDcAmt.Location = new Point(170, 419);
            txt_cashDcAmt.Name = "txt_cashDcAmt";
            txt_cashDcAmt.Size = new Size(148, 23);
            txt_cashDcAmt.TabIndex = 31;
            // 
            // txt_cashDcRt
            // 
            txt_cashDcRt.Location = new Point(170, 387);
            txt_cashDcRt.Name = "txt_cashDcRt";
            txt_cashDcRt.Size = new Size(148, 23);
            txt_cashDcRt.TabIndex = 30;
            // 
            // label17
            // 
            label17.AutoSize = true;
            label17.Location = new Point(13, 419);
            label17.Name = "label17";
            label17.Size = new Size(130, 15);
            label17.TabIndex = 29;
            label17.Text = "Cash Discount Amount";
            // 
            // label18
            // 
            label18.AutoSize = true;
            label18.Location = new Point(13, 390);
            label18.Name = "label18";
            label18.Size = new Size(109, 15);
            label18.TabIndex = 28;
            label18.Text = "Cash Discount Rate";
            // 
            // textBox19
            // 
            textBox19.Location = new Point(170, 358);
            textBox19.Name = "textBox19";
            textBox19.Size = new Size(148, 23);
            textBox19.TabIndex = 27;
            // 
            // textBox20
            // 
            textBox20.Location = new Point(170, 329);
            textBox20.Name = "textBox20";
            textBox20.Size = new Size(148, 23);
            textBox20.TabIndex = 26;
            // 
            // label19
            // 
            label19.AutoSize = true;
            label19.Location = new Point(13, 361);
            label19.Name = "label19";
            label19.Size = new Size(104, 15);
            label19.TabIndex = 25;
            label19.Text = "Tax Amount Fields";
            // 
            // label20
            // 
            label20.AutoSize = true;
            label20.Location = new Point(13, 332);
            label20.Name = "label20";
            label20.Size = new Size(83, 15);
            label20.TabIndex = 24;
            label20.Text = "Tax Rate Fields";
            // 
            // textBox21
            // 
            textBox21.Location = new Point(170, 300);
            textBox21.Name = "textBox21";
            textBox21.Size = new Size(148, 23);
            textBox21.TabIndex = 23;
            // 
            // txt_invcAdjustReason
            // 
            txt_invcAdjustReason.Location = new Point(170, 271);
            txt_invcAdjustReason.Name = "txt_invcAdjustReason";
            txt_invcAdjustReason.Size = new Size(148, 23);
            txt_invcAdjustReason.TabIndex = 22;
            // 
            // label21
            // 
            label21.AutoSize = true;
            label21.Location = new Point(13, 308);
            label21.Name = "label21";
            label21.Size = new Size(126, 15);
            label21.TabIndex = 21;
            label21.Text = "Taxable Amount Fields";
            // 
            // label22
            // 
            label22.AutoSize = true;
            label22.Location = new Point(13, 279);
            label22.Name = "label22";
            label22.Size = new Size(151, 15);
            label22.TabIndex = 20;
            label22.Text = "Invoice Adjustment Reason";
            // 
            // txt_rfdRsnCd
            // 
            txt_rfdRsnCd.Location = new Point(170, 242);
            txt_rfdRsnCd.Name = "txt_rfdRsnCd";
            txt_rfdRsnCd.Size = new Size(148, 23);
            txt_rfdRsnCd.TabIndex = 19;
            // 
            // label23
            // 
            label23.AutoSize = true;
            label23.Location = new Point(13, 250);
            label23.Name = "label23";
            label23.Size = new Size(117, 15);
            label23.TabIndex = 17;
            label23.Text = "Refund Reason Code";
            label23.Click += label23_Click;
            // 
            // label24
            // 
            label24.AutoSize = true;
            label24.Location = new Point(13, 221);
            label24.Name = "label24";
            label24.Size = new Size(72, 15);
            label24.TabIndex = 16;
            label24.Text = "Refund Date";
            // 
            // label25
            // 
            label25.AutoSize = true;
            label25.Location = new Point(13, 192);
            label25.Name = "label25";
            label25.Size = new Size(70, 15);
            label25.TabIndex = 13;
            label25.Text = "Cancel Date";
            // 
            // label26
            // 
            label26.AutoSize = true;
            label26.Location = new Point(13, 163);
            label26.Name = "label26";
            label26.Size = new Size(115, 15);
            label26.TabIndex = 12;
            label26.Text = "Cancel Request Date";
            // 
            // label27
            // 
            label27.AutoSize = true;
            label27.Location = new Point(13, 134);
            label27.Name = "label27";
            label27.Size = new Size(105, 15);
            label27.TabIndex = 9;
            label27.Text = "Stock Release Date";
            // 
            // txt_custNm
            // 
            txt_custNm.Location = new Point(170, 91);
            txt_custNm.Name = "txt_custNm";
            txt_custNm.Size = new Size(148, 23);
            txt_custNm.TabIndex = 7;
            // 
            // txt_custTpin
            // 
            txt_custTpin.Location = new Point(170, 62);
            txt_custTpin.Name = "txt_custTpin";
            txt_custTpin.Size = new Size(148, 23);
            txt_custTpin.TabIndex = 6;
            // 
            // label29
            // 
            label29.AutoSize = true;
            label29.Location = new Point(13, 99);
            label29.Name = "label29";
            label29.Size = new Size(94, 15);
            label29.TabIndex = 5;
            label29.Text = "Customer Name";
            // 
            // label30
            // 
            label30.AutoSize = true;
            label30.Location = new Point(13, 70);
            label30.Name = "label30";
            label30.Size = new Size(87, 15);
            label30.TabIndex = 4;
            label30.Text = "Customer TPIN";
            // 
            // txt_orgIncNo
            // 
            txt_orgIncNo.Location = new Point(170, 30);
            txt_orgIncNo.Name = "txt_orgIncNo";
            txt_orgIncNo.Size = new Size(148, 23);
            txt_orgIncNo.TabIndex = 2;
            // 
            // label32
            // 
            label32.AutoSize = true;
            label32.Location = new Point(13, 38);
            label32.Name = "label32";
            label32.Size = new Size(109, 15);
            label32.TabIndex = 0;
            label32.Text = "Original Invoice No";
            // 
            // lbl_ItemTitle
            // 
            lbl_ItemTitle.AutoSize = true;
            lbl_ItemTitle.Font = new Font("Segoe UI", 15F);
            lbl_ItemTitle.Location = new Point(21, 603);
            lbl_ItemTitle.Name = "lbl_ItemTitle";
            lbl_ItemTitle.Size = new Size(63, 28);
            lbl_ItemTitle.TabIndex = 2;
            lbl_ItemTitle.Text = "Items:";
            // 
            // dgv_Items
            // 
            dgv_Items.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv_Items.Columns.AddRange(new DataGridViewColumn[] { itemNm, itemCd, qtyUnitCd, prc, dcRt, pkg, totAmt, splyAmt, itemClsCd });
            dgv_Items.Location = new Point(21, 643);
            dgv_Items.Name = "dgv_Items";
            dgv_Items.Size = new Size(946, 150);
            dgv_Items.TabIndex = 4;
            // 
            // itemNm
            // 
            itemNm.HeaderText = "Item Name";
            itemNm.Name = "itemNm";
            // 
            // itemCd
            // 
            itemCd.HeaderText = "Item Code";
            itemCd.Name = "itemCd";
            // 
            // qtyUnitCd
            // 
            qtyUnitCd.HeaderText = "Quantity Unit Code";
            qtyUnitCd.Name = "qtyUnitCd";
            // 
            // prc
            // 
            prc.HeaderText = "Price";
            prc.Name = "prc";
            // 
            // dcRt
            // 
            dcRt.HeaderText = "Discount Rate";
            dcRt.Name = "dcRt";
            // 
            // pkg
            // 
            pkg.HeaderText = "Package Quantity";
            pkg.Name = "pkg";
            // 
            // totAmt
            // 
            totAmt.HeaderText = "Item Total Amount";
            totAmt.Name = "totAmt";
            // 
            // splyAmt
            // 
            splyAmt.HeaderText = "Supply Amount";
            splyAmt.Name = "splyAmt";
            // 
            // itemClsCd
            // 
            itemClsCd.HeaderText = "Item Classification Code";
            itemClsCd.Name = "itemClsCd";
            // 
            // btn_AddItem
            // 
            btn_AddItem.Location = new Point(973, 643);
            btn_AddItem.Name = "btn_AddItem";
            btn_AddItem.Size = new Size(75, 23);
            btn_AddItem.TabIndex = 5;
            btn_AddItem.Text = "Add Item";
            btn_AddItem.UseVisualStyleBackColor = true;
            btn_AddItem.Click += btn_AddItem_Click;
            // 
            // frmInvoice
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1214, 805);
            Controls.Add(btn_AddItem);
            Controls.Add(dgv_Items);
            Controls.Add(lbl_ItemTitle);
            Controls.Add(grbOptional);
            Controls.Add(grbRequired);
            Name = "frmInvoice";
            Text = "Create Invoice";
            grbRequired.ResumeLayout(false);
            grbRequired.PerformLayout();
            grbOptional.ResumeLayout(false);
            grbOptional.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgv_Items).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private GroupBox grbRequired;
        private Label label3;
        private Label label4;
        private TextBox txt_bhfId;
        private TextBox txt_tpin;
        private Label lbl_bhfId;
        private Label lbl_tpin;
        private TextBox txt_exchangeRt;
        private Label label13;
        private Label label14;
        private TextBox txt_currencyTyCd;
        private TextBox txt_modrNm;
        private Label label15;
        private Label label16;
        private TextBox txt_modrId;
        private TextBox txt_regrId;
        private Label label9;
        private Label label10;
        private TextBox txt_totAmt;
        private TextBox txt_totTaxAmt;
        private Label label11;
        private Label label12;
        private TextBox txt_totTaxblAmt;
        private TextBox txt_totItemCnt;
        private Label label5;
        private Label label6;
        private Label label7;
        private Label label8;
        private GroupBox grbOptional;
        private TextBox txt_cashDcAmt;
        private TextBox txt_cashDcRt;
        private Label label17;
        private Label label18;
        private TextBox textBox19;
        private TextBox textBox20;
        private Label label19;
        private Label label20;
        private TextBox textBox21;
        private TextBox txt_invcAdjustReason;
        private Label label21;
        private Label label22;
        private TextBox txt_rfdRsnCd;
        private Label label23;
        private Label label24;
        private Label label25;
        private Label label26;
        private Label label27;
        private Label label28;
        private TextBox txt_custNm;
        private TextBox txt_custTpin;
        private Label label29;
        private Label label30;
        private TextBox txt_cisInvcNo;
        private TextBox txt_orgIncNo;
        private Label label31;
        private Label label32;
        private TextBox txt_regrNm;
        private Label label33;
        private ComboBox cmb_salesSttsCd;
        private ComboBox cmb_rcptTyCd;
        private ComboBox cmb_salesTyCd;
        private DateTimePicker dtp_salesDt;
        private TextBox txt_dbtRsnCd;
        private TextBox txt_destnCountryCd;
        private Label label34;
        private Label label35;
        private TextBox txt_lpoNumber;
        private TextBox txt_saleCtyCd;
        private Label label36;
        private Label label37;
        private TextBox sel_remark;
        private Label label38;
        private Label label39;
        private ComboBox cmb_pmtTyCd;
        private DateTimePicker dtp_rfdDt;
        private DateTimePicker dtp_cnclReqDt;
        private DateTimePicker dtp_stockRlsDt;
        private DateTimePicker dtp_cfmDt;
        private DateTimePicker dtp_cnclDt;
        private ComboBox cmb_prchrAcptcYn;
        private Label lbl_ItemTitle;
        private DataGridView dgv_Items;
        private DataGridViewTextBoxColumn itemNm;
        private DataGridViewTextBoxColumn itemCd;
        private DataGridViewTextBoxColumn qtyUnitCd;
        private DataGridViewTextBoxColumn prc;
        private DataGridViewTextBoxColumn dcRt;
        private DataGridViewTextBoxColumn pkg;
        private DataGridViewTextBoxColumn totAmt;
        private Button btn_AddItem;
        private DataGridViewTextBoxColumn splyAmt;
        private DataGridViewTextBoxColumn itemClsCd;
        private Button btn_CreateInvoice;
    }
}