using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zambia.Invoice.Models.Context;
using Zambia.Invoice.Models.ExistingTables;

namespace Zambia.Invoice.Services
{
    public class ZRAInvoiceService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public ZRAInvoiceService(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        // ZRA_INVOICE_H methods
        public async Task<List<ZRA_INVOICE_H>> GetAllInvoiceHeadersAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ZRA_INVOICE_Hs.ToListAsync();
        }

        public async Task<ZRA_INVOICE_H?> GetInvoiceHeaderByInvoiceNoAsync(string invoiceNo)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ZRA_INVOICE_Hs
                .FirstOrDefaultAsync(h => h.IDINVC == invoiceNo);
        }

        public async Task<ZRA_INVOICE_H?> GetInvoiceHeaderByIdAsync(string idInvc)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ZRA_INVOICE_Hs
                .FirstOrDefaultAsync(h => h.IDINVC == idInvc);
        }

        // ZRA_INVOICE_D methods
        public async Task<List<ZRA_INVOICE_D>> GetAllInvoiceDetailsAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ZRA_INVOICE_Ds.ToListAsync();
        }

        public async Task<List<ZRA_INVOICE_D>> GetInvoiceDetailsByInvoiceNoAsync(string invoiceNo)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ZRA_INVOICE_Ds
                .Where(d => d.INVOICENO == invoiceNo)
                .ToListAsync();
        }

        public async Task<List<ZRA_INVOICE_D>> GetInvoiceDetailsByIdAsync(string idInvc)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ZRA_INVOICE_Ds
                .Where(d => d.INVOICENO == idInvc)
                .ToListAsync();
        }

        // Combined methods for header with details
        public async Task<(ZRA_INVOICE_H? Header, List<ZRA_INVOICE_D> Details)> GetCompleteInvoiceByInvoiceNoAsync(string invoiceNo)
        {
            using var context = _contextFactory.CreateDbContext();
            
            var header = await context.ZRA_INVOICE_Hs
                .FirstOrDefaultAsync(h => h.IDINVC == invoiceNo);
            
            var details = await context.ZRA_INVOICE_Ds
                .Where(d => d.INVOICENO == invoiceNo)
                .ToListAsync();

            return (header, details);
        }

        public async Task<(ZRA_INVOICE_H? Header, List<ZRA_INVOICE_D> Details)> GetCompleteInvoiceByIdAsync(string idInvc)
        {
            using var context = _contextFactory.CreateDbContext();
            
            var header = await context.ZRA_INVOICE_Hs
                .FirstOrDefaultAsync(h => h.IDINVC == idInvc);
            
            var details = await context.ZRA_INVOICE_Ds
                .Where(d => d.INVOICENO == idInvc)
                .ToListAsync();

            return (header, details);
        }
    }
}