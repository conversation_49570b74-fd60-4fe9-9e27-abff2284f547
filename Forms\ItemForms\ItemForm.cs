﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Zambia.Invoice.Services;
using Microsoft.Extensions.DependencyInjection;
using Zambia.Invoice.Models;

namespace Zambia.Invoice.Forms.ItemForms
{
    public partial class ItemForm : Form
    {
        private List<ItemListEntity> _allItems = new List<ItemListEntity>();

        private readonly ItemService _itemService;
        private readonly IServiceProvider _serviceProvider;

        public ItemForm(ItemService itemService, IServiceProvider serviceProvider)
        {
            _itemService = itemService;
            _serviceProvider = serviceProvider;
            InitializeComponent();
        }

        // Add a button click event to open frm_AddItem
        private void btn_AddItem_Click(object sender, EventArgs e)
        {
            var addItemForm = _serviceProvider.GetRequiredService<frm_AddItem>();
            addItemForm.Show();
        }

        private void dgv_Items_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private async void btn_RegreshItems_Click(object sender, EventArgs e)
        {
            LoadingForm loadingForm = null;
            try
            {
                loadingForm = new LoadingForm();
                loadingForm.Show(this);

                await _itemService.DeleteAllItemsDataAsync();
                await _itemService.LoadAndSaveAllItemsAsync();
                var count = await _itemService.GetRecordCountsAsync();
                if (count.ItemCount != 0)
                {
                    MessageBox.Show($"{count.ItemCount} Items have been loaded");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error: {ex.Message}");
            }
            finally
            {
                // Update the DataGridView with fresh data
                try
                {
                    var data = await _itemService.GetAllItemsFromDatabase();

                 

                    // Flatten the data to show all ItemListEntity properties
                    var flattenedData = data.SelectMany(response => response.itemList).ToList();
                    _allItems = flattenedData;
                    dgv_Items.DataSource = _allItems;

                    if (dgv_Items.Columns.Count > 0)
                    {
                        dgv_Items.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading data to grid: {ex.Message}");
                }

                loadingForm?.Close();
            }
        }

        private async void ItemForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Load existing data when form opens
                var data = await _itemService.GetAllItemsFromDatabase();

                if (data.Any())
                {
                    var flattenedData = data.SelectMany(response => response.itemList).ToList();

                    _allItems = flattenedData;
                    dgv_Items.DataSource = _allItems;

                    if (dgv_Items.Columns.Count > 0)
                    {
                        dgv_Items.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading existing data: {ex.Message}");
            }
        }

        private void btn_AddItem_Click_1(object sender, EventArgs e)
        {
            var itemForm = _serviceProvider.GetRequiredService<frm_AddItem>();
            itemForm.Show();
        }

        private void btn_Search_Click(object sender, EventArgs e)
        {
            string searchTerm = txt_Search.Text.Trim().ToLower();

            if (string.IsNullOrEmpty(searchTerm))
            {
                dgv_Items.DataSource = _allItems;
            }
            else
            {
                var filtered = _allItems
                    .Where(item =>
                        (!string.IsNullOrEmpty(item.itemStdNm) && item.itemClsCd.ToLower().Contains(searchTerm)) ||
                        (!string.IsNullOrEmpty(item.itemNm) && item.itemCd.ToLower().Contains(searchTerm)) ||
                        (!string.IsNullOrEmpty(item.regBhfId) && item.regBhfId.ToLower().Contains(searchTerm))
                    ).ToList();

                dgv_Items.DataSource = filtered;
            }

            dgv_Items.Refresh();
        }

    }
}
