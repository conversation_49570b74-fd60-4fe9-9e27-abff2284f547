﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Zambia.Invoice.Services;
using Zambia.Invoice.Models;

namespace Zambia.Invoice.Forms.ItemForms
{
    public partial class frmSelectItem : Form
    {
        private readonly ItemService _itemService;
        private List<ItemListEntity> _allItems = new List<ItemListEntity>();

        // Property to hold selected items
        public List<ItemListEntity> SelectedItems { get; private set; } = new List<ItemListEntity>();

        public frmSelectItem(ItemService itemService)
        {
            _itemService = itemService;
            InitializeComponent();
        }

        private async void frmSelectItem_Load(object sender, EventArgs e)
        {
            await LoadItems();
        }

        private async Task LoadItems()
        {
            LoadingForm loadingForm = null;
            try
            {
                loadingForm = new LoadingForm();
                loadingForm.Show(this);

                var data = await _itemService.GetAllItemsFromDatabase();

                if (data.Any())
                {
                    var flattenedData = data.SelectMany(response => response.itemList).ToList();
                    _allItems = flattenedData;
                    dataGridView1.DataSource = _allItems;

                    // Enable multi-selection
                    dataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                    dataGridView1.MultiSelect = true;

                    if (dataGridView1.Columns.Count > 0)
                    {
                        dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading items: {ex.Message}");
            }
            finally
            {
                loadingForm?.Close();
            }
        }

        private void btnSelect_Click(object sender, EventArgs e)
        {
            // Get selected items from the grid
            foreach (DataGridViewRow row in dataGridView1.SelectedRows)
            {
                if (row.DataBoundItem is ItemListEntity item)
                    SelectedItems.Add(item);
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
