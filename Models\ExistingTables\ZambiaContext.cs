﻿//// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
//#nullable disable
//using System;
//using System.Collections.Generic;
//using Microsoft.EntityFrameworkCore;

//namespace Zambia.Invoice.Models.ExistingTables;

//public partial class ZambiaContext : DbContext
//{
//    public ZambiaContext(DbContextOptions<ZambiaContext> options)
//        : base(options)
//    {
//    }

//    public virtual DbSet<ZRA_INVOICE_D> ZRA_INVOICE_Ds { get; set; }

//    public virtual DbSet<ZRA_INVOICE_H> ZRA_INVOICE_Hs { get; set; }

//    protected override void OnModelCreating(ModelBuilder modelBuilder)
//    {
//        modelBuilder.Entity<ZRA_INVOICE_D>(entity =>
//        {
//            entity
//                .HasNoKey()
//                .ToView("ZRA_INVOICE_D");

//            entity.Property(e => e.IDITEM)
//                .IsRequired()
//                .HasMaxLength(12)
//                .IsUnicode(false);
//            entity.Property(e => e.INVOICENO)
//                .IsRequired()
//                .HasMaxLength(9);
//            entity.Property(e => e.INV_TYPE)
//                .IsRequired()
//                .HasMaxLength(3)
//                .IsUnicode(false);
//            entity.Property(e => e.RATETAX1).HasColumnType("decimal(21, 4)");
//            entity.Property(e => e.TEXTDESC)
//                .IsRequired()
//                .HasMaxLength(12)
//                .IsUnicode(false);
//        });

//        modelBuilder.Entity<ZRA_INVOICE_H>(entity =>
//        {
//            entity
//                .HasNoKey()
//                .ToView("ZRA_INVOICE_H");

//            entity.Property(e => e.AMTINVCTOT).HasColumnType("money");
//            entity.Property(e => e.AMTTAX1).HasColumnType("money");
//            entity.Property(e => e.AMTTAXTOT).HasColumnType("money");
//            entity.Property(e => e.CNTBTCH)
//                .IsRequired()
//                .HasMaxLength(1)
//                .IsUnicode(false);
//            entity.Property(e => e.CNTITEM)
//                .IsRequired()
//                .HasMaxLength(2)
//                .IsUnicode(false);
//            entity.Property(e => e.CODECURN)
//                .HasMaxLength(10)
//                .IsUnicode(false);
//            entity.Property(e => e.DATEINVC)
//                .HasMaxLength(8000)
//                .IsUnicode(false);
//            entity.Property(e => e.IDCUST).HasMaxLength(6);
//            entity.Property(e => e.IDINVC)
//                .IsRequired()
//                .HasMaxLength(9);
//            entity.Property(e => e.IDTAXREGI1)
//                .IsRequired()
//                .HasMaxLength(20);
//            entity.Property(e => e.INVCAPPLTO).HasMaxLength(21);
//            entity.Property(e => e.NAMECUST).HasMaxLength(50);
//            entity.Property(e => e.SPECINST).HasMaxLength(100);
//            entity.Property(e => e.ZRARCPT)
//                .IsRequired()
//                .HasMaxLength(1)
//                .IsUnicode(false);
//            entity.Property(e => e.ZRASDC)
//                .IsRequired()
//                .HasMaxLength(13)
//                .IsUnicode(false);
//        });

//        OnModelCreatingPartial(modelBuilder);
//    }

//    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
//}