﻿using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Zambia.Invoice.Forms.ItemForms;
using Zambia.Invoice.Global;
using Zambia.Invoice.Helpers;
using Zambia.Invoice.Models;
using Zambia.Invoice.Services;

namespace Zambia.Invoice.Forms.InvoiceForms
{
    public partial class frmInvoice : Form
    {

        private readonly IServiceProvider _serviceProvider;
        private List<ItemListEntity> _selectedItems = new List<ItemListEntity>();
        private readonly InvoiceService _invoiceCreateService;
        private InvoiceCreateModel create = new InvoiceCreateModel();
        private readonly CIS_Helper _cisHelper;
        private string _currentCisNumber;



        public frmInvoice(IServiceProvider serviceProvider, InvoiceService invoiceCreateService, CIS_Helper cis_Helper)
        {
            _cisHelper = cis_Helper;
            _invoiceCreateService = invoiceCreateService;
            //_invoiceCreateService = serviceProvider.GetRequiredService<InvoiceCreateService>();
            create.itemList = new List<ItemListCreate>();
            _serviceProvider = serviceProvider;

            InitializeComponent();
            this.Load += async (sender, e) => await frmInvoice_LoadAsync(sender, e);
        }

        private void cmb_salesTyCd_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void grbOptional_Enter(object sender, EventArgs e)
        {

        }

        private void dateTimePicker4_ValueChanged(object sender, EventArgs e)
        {

        }

        private async Task frmInvoice_LoadAsync(object sender, EventArgs e)
        {
            // Generate new CIS invoice number
            _currentCisNumber = await _cisHelper.CreateCISInvoiceNumberAsync();
            txt_cisInvcNo.Text = _currentCisNumber;

            txt_bhfId.Text = GlobalConfig.BranchCode;
            // Set default selections for combo boxes
            cmb_rcptTyCd.SelectedIndex = 0; // "Sales"
            cmb_pmtTyCd.SelectedIndex = 0;  // "Cash"
            cmb_salesSttsCd.SelectedIndex = 0; // "Pending"
            cmb_prchrAcptcYn.SelectedIndex = 0;
            cmb_salesTyCd.SelectedIndex = 0;// Normal
            cmb_currencyTyCd.SelectedIndex = 0;

            // cmb_prchrAcptcYn remains unselected (SelectedIndex = -1)
            // cmb_prchrAcptcYn.SelectedIndex = -1;
        }

        private void btn_AddItem_Click(object sender, EventArgs e)
        {
            using (var selectItemForm = _serviceProvider.GetRequiredService<frmSelectItem>())
            {
                if (selectItemForm.ShowDialog() == DialogResult.OK)
                {
                    // Get selected items from SelectItemForm
                    var selectedItems = selectItemForm.SelectedItems;
                    // Add them to our list (avoiding duplicates)
                    foreach (var item in selectedItems)
                    {
                        if (!_selectedItems.Any(x => x.Id == item.Id))
                        {
                            _selectedItems.Add(item);
                        }
                    }

                    // Clear existing itemList and rebuild it
                    create.itemList.Clear();

                    // Update the DataGridView with selected items
                    dgv_Items.DataSource = null;
                    dgv_Items.Rows.Clear();

                    var counter = 0;
                    foreach (var item in _selectedItems.ToList())
                    {
                        counter++;

                        // Calculate correct tax amounts for tax-inclusive pricing
                        decimal taxInclusivePrice = item.dftPrc;
                        decimal taxExclusiveAmount = 0;
                        decimal taxAmount = 0;

                        if (item.vatCatCd == "A")
                        {
                            taxExclusiveAmount = taxInclusivePrice / 1.16m; // Remove 16% VAT
                            taxAmount = taxInclusivePrice - taxExclusiveAmount;
                        }
                        else
                        {
                            // For non-VAT items
                            taxExclusiveAmount = taxInclusivePrice;
                            taxAmount = 0;
                        }

                        dgv_Items.Rows.Add(
                           counter, // Item sequence number
                           item.itemCd, // Item code
                           item.itemClsCd, // Classification code
                           item.itemNm, // Item name
                           item.pkgUnitCd, // Packaging unit code
                           1, // Package quantity
                           "EA", // Quantity unit code (use exact code)
                           1, // Quantity
                           item.dftPrc, // Unit price (tax inclusive)
                           1 * item.dftPrc, // Supply amount (qty × prc)
                           item.vatCatCd, // VAT category code
                           item.dftPrc // totAmt (same as price for tax-inclusive)
                       );
                    }

                    // Calculate and update totals
                    UpdateTotals();

                    if (dgv_Items.Columns.Count > 0)
                    {
                        dgv_Items.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                    }
                }
            }
        }

        private void UpdateTotals()
        {
            decimal totalTaxableAmount = 0;
            decimal totalTaxAmount = 0;
            decimal totalAmount = 0;

            foreach (var item in _selectedItems)
            {
                decimal itemTotal = 1 * item.dftPrc; // qty × price (tax-inclusive)

                if (item.vatCatCd == "A")
                {
                    decimal taxExclusiveAmount = Math.Round(itemTotal / 1.16m, 4); // Remove 16% VAT, round to 4 decimals
                    decimal taxAmount = Math.Round(itemTotal - taxExclusiveAmount, 4); // Tax amount, round to 4 decimals

                    totalTaxableAmount += taxExclusiveAmount;
                    totalTaxAmount += taxAmount;
                }
                else
                {
                    // For non-VAT items, full amount is taxable base, no tax
                    totalTaxableAmount += Math.Round(itemTotal, 4);
                }

                totalAmount += itemTotal;
            }

            // Update the text boxes
            txt_totTaxblAmt.Text = Math.Round(totalTaxableAmount, 2).ToString("F2");
            txt_totTaxAmt.Text = Math.Round(totalTaxAmount, 2).ToString("F2");
            txt_totAmt.Text = Math.Round(totalAmount, 2).ToString("F2");
            txt_totItemCnt.Text = _selectedItems.Count.ToString();
        }

        private void label11_Click(object sender, EventArgs e)
        {

        }


        private void frmInvoice_Load_1(object sender, EventArgs e)
        {

        }

        private async void btn_CreateInvoice_Click_1(object sender, EventArgs e)
        {
            // TODO -> Think about adding a json field saving the data that goes to SaveInvoiceAsync(create)
            // maybe save it as Json?
            LoadingForm loadingForm = null;
            try
            {
                loadingForm = new LoadingForm();
                loadingForm.Show(this);

                create.tpin = txt_tpin.Text;
                create.cisInvcNo = txt_cisInvcNo.Text;
                create.bhfId = txt_bhfId.Text;
                create.salesTyCd = "N"; // Ensure exactly "N", not longer
                //create.rcptTyCd = cmb_rcptTyCd.Text;
                create.rcptTyCd = "S";// Sale
                // create.pmtTyCd = cmb_pmtTyCd.Text;
                create.pmtTyCd = "01";  // Cash
                //create.salesSttsCd = cmb_salesSttsCd.Text;
                create.salesSttsCd = "02";
                create.salesDt = dtp_salesDt.Value.ToString("yyyyMMdd");

                // Fix: Use proper DateTime formatting for cfmDt
                create.cfmDt = dtp_cfmDt.Value.ToString("yyyyMMddHHmmss");

                // Fix: Add missing customer fields (required)
                //create.custTpin = "9999999999"; // Add customer TPIN textbox
                //create.custNm = "Walk-in Customer";  // Add customer name textbox
                create.custTpin = null;    // Use null instead of empty string
                create.custNm = null;      // Use null instead of empty string

                // Fix: Stock release date (can be null for services or same as cfmDt for goods)
                create.stockRlsDt = null; // or dtp_cfmDt.Value.ToString("yyyyMMddHHmmss") for physical goods

                // For Normal Sales -> Not cancelations
                create.cnclDt = "20000101000000";
                create.cnclReqDt = "20000101000000";
                create.rfdDt = "20000101000000";
                create.lpoNumber = null;

                // Fix: Add missing required fields
                create.rfdRsnCd = null;
                create.saleCtyCd = "1";
                create.orgInvcNo = 0;

                // Add missing optional fields that API might expect
                create.destnCountryCd = "";
                create.dbtRsnCd = "";
                create.invcAdjustReason = "";
                create.remark = "";

                int itemCount;
                if (int.TryParse(txt_totItemCnt.Text, out itemCount))
                {
                    create.totItemCnt = itemCount;
                }
                else
                {
                    create.totItemCnt = 0;
                }

                create.totTaxblAmt = Convert.ToDouble(txt_totTaxblAmt.Text);
                create.totTaxAmt = Convert.ToDouble(txt_totTaxAmt.Text);

                // Fix: Use correct variable for totAmt and keep as double, not int
                double totAmtValue;
                if (double.TryParse(txt_totAmt.Text, out totAmtValue))
                {
                    create.totAmt = totAmtValue; // Keep as double, don't cast to int
                }
                else
                {
                    create.totAmt = 0;
                }

                create.regrId = txt_regrId.Text;
                create.regrNm = txt_regrNm.Text;

                // Fix: Add required modifier fields
                create.modrId = txt_regrId.Text;
                create.modrNm = txt_regrNm.Text;

                create.currencyTyCd = cmb_currencyTyCd.Text;
                create.exchangeRt = txt_exchangeRt.Text.Trim();
                //create.prchrAcptcYn = cmb_prchrAcptcYn.Text;
                create.prchrAcptcYn = "N"; // im not accepting the invoice

                // Add missing tax rate and amount fields
                create.taxRtA = 16;
                create.taxRtB = 16;
                create.taxRtC1 = 0;
                create.taxRtC2 = 0;
                create.taxRtC3 = 0;
                create.taxRtD = 0;
                create.taxRtRvat = 16;
                create.taxRtE = 0;
                create.taxRtF = 10;
                create.taxRtIpl1 = 5;
                create.taxRtIpl2 = 0;
                create.taxRtTl = 1.5;
                create.taxRtEcm = 5;
                create.taxRtExeeg = 3;
                create.taxRtTot = 0;

                // Set tax amounts
                create.taxAmtA = Convert.ToDouble(txt_totTaxAmt.Text);
                create.taxAmtB = 0;
                create.taxAmtC1 = 0;
                create.taxAmtC2 = 0;
                create.taxAmtC3 = 0;
                create.taxAmtD = 0;
                create.taxAmtRvat = 0;
                create.taxAmtE = 0;
                create.taxAmtF = 0;
                create.taxAmtIpl1 = 0;
                create.taxAmtIpl2 = 0;
                create.taxAmtTl = 0;
                create.taxAmtEcm = 0;
                create.taxAmtExeeg = 0;
                create.taxAmtTot = 0;

                // Set taxable amounts
                create.taxblAmtA = Convert.ToDouble(txt_totTaxblAmt.Text);
                create.taxblAmtB = 0;
                create.taxblAmtC1 = 0;
                create.taxblAmtC2 = 0;
                create.taxblAmtC3 = 0;
                create.taxblAmtD = 0;
                create.taxblAmtRvat = 0;
                create.taxblAmtE = 0;
                create.taxblAmtF = 0;
                create.taxblAmtIpl1 = 0;
                create.taxblAmtIpl2 = 0;
                create.taxblAmtTl = 0;
                create.taxblAmtEcm = 0;
                create.taxblAmtExeeg = 0;
                create.taxblAmtTot = 0;

                // Add cash discount fields (set to 0 for no discount)
                create.cashDcRt = 0;
                create.cashDcAmt = 0;
                // create.CIS = _currentCisNumber;

                for (int i = 0; i < create.itemList.Count; i++)
                {
                    create.itemList[i].qtyUnitCd = "EA";
                }

                // convert create to json
                //string json = JsonConvert.SerializeObject(create, Formatting.Indented);

                var response = await _invoiceCreateService.SaveInvoiceAPI_Async(create);
                
                if (response != null && response.resultCd == "000")
                {
                    // Success - save the invoice response
                    await _cisHelper.SaveSuccessfulInvoiceDB_Async("",_currentCisNumber, response);
                    MessageBox.Show($"Invoice saved successfully: {response.resultMsg}");
                    
                    // Generate new CIS number for next invoice
                    _currentCisNumber = await _cisHelper.CreateCISInvoiceNumberAsync();
                    txt_cisInvcNo.Text = _currentCisNumber;
                }
                else
                {
                    // Failed - remove the CIS number from database
                    await _cisHelper.RemoveCISInvoiceNumberAsync(_currentCisNumber);
                    MessageBox.Show($"Invoice creation failed: {response?.resultMsg ?? "Unknown error"}");
                }
            }
            catch (Exception ex)
            {
                // Error occurred - remove the CIS number from database
                if (!string.IsNullOrEmpty(_currentCisNumber))
                {
                    await _cisHelper.RemoveCISInvoiceNumberAsync(_currentCisNumber);
                }
                MessageBox.Show($"Error: {ex.Message}");
            }
            finally
            {
                loadingForm?.Close();
            }
        }

        private void dgv_Items_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void dgv_Items_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
        {
            // Skip the 'new row' placeholder at the bottom
            if (e.RowIndex < 0 || dgv_Items.Rows[e.RowIndex].IsNewRow)
                return;

            DataGridViewRow row = dgv_Items.Rows[e.RowIndex];

            // Use the correct column names from designer
            var itemSeq = Convert.ToInt32(row.Cells["col_sequence"].Value ?? 0);
            var itemCd = row.Cells["col_itemCode"].Value?.ToString();
            var itemClsCd = row.Cells["col_classification_code"].Value?.ToString();
            var itemNm = row.Cells["col_itemName"].Value?.ToString();
            var pkgUnitCd = row.Cells["col_packagingCode"].Value?.ToString();
            var pkg = Convert.ToDecimal(row.Cells["col_packageQuantity"].Value ?? 1);
            var qtyUnitCd = "EA"; // Use exact code
            var qty = Convert.ToDecimal(row.Cells["col_quantity"].Value ?? 1);
            var prc = Convert.ToDecimal(row.Cells["col_unitPrice"].Value ?? 0);
            var vatCatCd = row.Cells["col_VATcode"].Value?.ToString();

            // Calculate amounts correctly for tax-inclusive pricing
            var splyAmt = qty * prc; // This is tax-inclusive

            decimal vatTaxblAmt = 0; // Tax-exclusive amount
            decimal vatAmt = 0; // Tax amount
            decimal totAmt = splyAmt; // Total is same as supply amount (no additional charges)

            if (vatCatCd == "A")
            {
                vatTaxblAmt = Math.Round(splyAmt / 1.16m, 4); // Remove 16% VAT, round to 4 decimals
                vatAmt = Math.Round(splyAmt - vatTaxblAmt, 4); // Tax amount, round to 4 decimals
            }
            else
            {
                vatTaxblAmt = Math.Round(splyAmt, 4); // For non-VAT items, round to 4 decimals
                vatAmt = 0;
            }

            // Add to your model with correct calculations
            create.itemList.Add(new ItemListCreate
            {
                itemSeq = itemSeq,
                itemCd = itemCd,
                itemClsCd = itemClsCd,
                itemNm = itemNm,
                pkgUnitCd = pkgUnitCd,
                pkg = Convert.ToDouble(pkg),
                qtyUnitCd = qtyUnitCd,
                qty = Convert.ToDouble(qty),
                prc = Convert.ToDouble(prc),
                splyAmt = Convert.ToDouble(splyAmt),
                dcRt = 0.0,
                dcAmt = 0.0,
                vatCatCd = vatCatCd,
                vatTaxblAmt = Convert.ToDouble(vatTaxblAmt), // Tax-exclusive amount
                vatAmt = Convert.ToDouble(vatAmt), // Tax amount
                exciseTaxblAmt = 0.0,
                exciseTxAmt = 0.0,
                iplTaxblAmt = 0.0,
                iplAmt = 0.0,
                tlTaxblAmt = 0.0,
                tlAmt = 0.0,
                totAmt = Convert.ToDouble(totAmt) // Tax-inclusive total
            });

            txt_totItemCnt.Text = create.itemList.Count.ToString();
        }

    }
}
