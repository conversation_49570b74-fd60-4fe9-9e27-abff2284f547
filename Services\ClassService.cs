﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zambia.Invoice.Models.Context;
using Zambia.Invoice.Models;
using Microsoft.EntityFrameworkCore;

namespace Zambia.Invoice.Services
{
    public class ClassService
    {
        private string baseUrl = "http://localhost:8080";
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly ConfigurationService _configService;

        public ClassService(IDbContextFactory<AppDbContext> contextFactory, ConfigurationService configService)
        {
            _contextFactory = contextFactory;
            _configService = configService;
        }

        private async Task<ClassModel> LoadDataAsync()
        {
            var apiService = new ApiService(baseUrl);
            var credentials = await _configService.GetCredentialsAsync();

            ClassModel classResponse = await apiService.PostAsync<object, ClassModel>(
                "sandboxvsdc/itemClass/selectItemsClass",
                new
                {
                    tpin = credentials.tpin,
                    bhfId = credentials.bhfId,
                    dvcSrlNo = credentials.dvcSrlNo,
                    lastReqDt = credentials.lastReqDt
                });

            return classResponse;
        }

        public async Task<ClassModelEntity> LoadAndSaveDataAsync()
        {
            try
            {
                // Load data from API
                var classResponse = await LoadDataAsync();

                // Save to database
                var savedClass = await SaveToDatabase(classResponse);

                return savedClass;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error loading and saving class data: {ex.Message}", ex);
            }
        }

        private async Task<ClassModelEntity> SaveToDatabase(ClassModel classResponse)
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // Create ClassModelEntity for database
                var classModelEntity = new ClassModelEntity
                {
                    Id = Guid.NewGuid(),
                    resultCd = classResponse.resultCd ?? string.Empty,
                    resultMsg = classResponse.resultMsg ?? string.Empty,
                    resultDt = classResponse.resultDt ?? string.Empty,
                    itemClsList = new List<ItemClsListEntity>()
                };

                // Process the itemClsList from the API response
                if (classResponse.data?.itemClsList != null)
                {
                    foreach (var item in classResponse.data.itemClsList)
                    {
                        var itemEntity = new ItemClsListEntity
                        {
                            Id = Guid.NewGuid(),
                            ClassModelEntityId = classModelEntity.Id,
                            itemClsCd = item.itemClsCd ?? string.Empty,
                            itemClsNm = item.itemClsNm ?? string.Empty,
                            itemClsLvl = item.itemClsLvl,
                            taxTyCd = item.taxTyCd?.ToString() ?? string.Empty,
                            mjrTgYn = item.mjrTgYn?.ToString() ?? string.Empty,
                            useYn = item.useYn ?? string.Empty
                        };

                        classModelEntity.itemClsList.Add(itemEntity);
                    }
                }

                // Add to context and save
                context.ClassModel.Add(classModelEntity);
                await context.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();

                // Return the saved entity with all relationships loaded
                var savedEntity = await context.ClassModel
                    .Include(c => c.itemClsList)
                    .FirstOrDefaultAsync(c => c.Id == classModelEntity.Id);

                return savedEntity ?? classModelEntity;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error saving class data to database: {ex.Message}", ex);
            }
        }

        // Method to retrieve saved data from database
        public async Task<List<ClassModelEntity>> GetAllClassesFromDatabase()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ClassModel
                .Include(c => c.itemClsList)
                .ToListAsync();
        }

        // Method to retrieve and reconstruct the original structure
        public async Task<ClassModel> GetClassModelAsync(Guid classModelEntityId)
        {
            using var context = _contextFactory.CreateDbContext();
            var entity = await context.ClassModel
                .Include(cme => cme.itemClsList)
                .FirstOrDefaultAsync(cme => cme.Id == classModelEntityId);

            if (entity == null) return null;

            return new ClassModel
            {
                resultCd = entity.resultCd,
                resultMsg = entity.resultMsg,
                resultDt = entity.resultDt,
                data = new DataClasses
                {
                    itemClsList = entity.itemClsList.Select(i => new ItemClsList
                    {
                        itemClsCd = i.itemClsCd,
                        itemClsNm = i.itemClsNm,
                        itemClsLvl = i.itemClsLvl,
                        taxTyCd = i.taxTyCd,
                        mjrTgYn = i.mjrTgYn,
                        useYn = i.useYn
                    }).ToList()
                }
            };
        }

        // Method to delete everything from the database
        public async Task<bool> DeleteAllDataAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // Delete in correct order due to foreign key constraints
                var itemClsListCount = await context.ItemClsList.CountAsync();
                if (itemClsListCount > 0)
                {
                    await context.Database.ExecuteSqlRawAsync("DELETE FROM [ZRA].[ItemClsList]");
                }

                var classModelCount = await context.ClassModel.CountAsync();
                if (classModelCount > 0)
                {
                    await context.Database.ExecuteSqlRawAsync("DELETE FROM [ZRA].[ClassModel]");
                }

                await transaction.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error deleting all class data: {ex.Message}", ex);
            }
        }

        // Alternative method using EF Core entities
        public async Task<bool> DeleteAllDataWithEntitiesAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var allClassModels = await context.ClassModel
                    .Include(c => c.itemClsList)
                    .ToListAsync();

                context.ClassModel.RemoveRange(allClassModels);
                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error deleting all class data with entities: {ex.Message}", ex);
            }
        }

        // Method to get count of records
        public async Task<(int ClassCount, int ItemClsListCount)> GetRecordCountsAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            var classCount = await context.ClassModel.CountAsync();
            var itemClsListCount = await context.ItemClsList.CountAsync();

            return (classCount, itemClsListCount);
        }
    }
}
