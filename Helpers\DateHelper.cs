﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Helpers
{
    public static class DateHelper
    {
        /// <summary>
        /// Returns format dd/MM/yyyy
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static string ToDateString(this string date)
        {
            DateTime parsedDate = DateTime.ParseExact(date, "yyyyMMdd", null);
            string formattedDate = parsedDate.ToString("dd/MM/yyyy");
            return formattedDate;
        }

        /// <summary>
        /// Returns date in format yyyyMMdd (NO SLASHES - required for ZRA API)
        /// </summary>
        /// <param name="date"></param>
        /// <param name="american"></param>
        /// <returns></returns>
        public static string ToDateString(this string date, bool american)
        {
            DateTime parsedDate = DateTime.ParseExact(date, "yyyyMMdd", null);
            string formattedDate = parsedDate.ToString("yyyyMMdd");  // Changed from "yyyy/MM/dd"
            return formattedDate;
        }

        /// <summary>
        /// Returns date in format yyyyMMddHHmmss (NO SLASHES - required for ZRA API)
        /// </summary>
        /// <param name="date"></param>
        /// <param name="american"></param>
        /// <param name="withTime"></param>
        /// <returns></returns>
        public static string ToDateString(this string date, bool american, bool withTime)
        {
            DateTime parsedDate = DateTime.ParseExact(date, "yyyyMMdd", null);
            string formattedDate = parsedDate.ToString("yyyyMMddHHmmss");  // Changed from "yyyy/MM/dd/HH/ss"
            return formattedDate;
        }
    }
}
