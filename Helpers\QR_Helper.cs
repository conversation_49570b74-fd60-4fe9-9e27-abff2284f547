﻿using QRCoder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Helpers
{
    public static class QR_Helper
    {
        public static string GenerateQRCodeImageWithLogo(string url)
        {
            if (string.IsNullOrEmpty(url))
                return string.Empty;

            using var qrGenerator = new QRCodeGenerator();
            using var qrCodeData = qrGenerator.CreateQrCode(url, QRCodeGenerator.ECCLevel.H);

            var assembly = Assembly.GetExecutingAssembly();
            var resourceName = "Zambia.Invoice.Assets.icon-192x192.png";

            Bitmap? logo = null;
            using var stream = assembly.GetManifestResourceStream(resourceName);
            if (stream != null)
            {
                logo = (Bitmap)Image.FromStream(stream);
            }

            using var qrCode = new QRCode(qrCodeData);
            // Increased pixelsPerModule from 20 to 30 for better quality
            // Reduced logo size from 15% to 12% for better scanning
            // Increased border from 6 to 10 for cleaner separation
            using var qrBitmap = qrCode.GetGraphic(30, Color.Black, Color.White, logo, 12, 10, true);

            using var ms = new MemoryStream();
            qrBitmap.Save(ms, System.Drawing.Imaging.ImageFormat.Png);

            logo?.Dispose();

            return $"data:image/png;base64,{Convert.ToBase64String(ms.ToArray())}";
        }
        //public static string GenerateQRCodeImageWithLogo(string url)
        //{
        //    if (string.IsNullOrEmpty(url))
        //        return string.Empty;

        //    using var qrGenerator = new QRCodeGenerator();
        //    using var qrCodeData = qrGenerator.CreateQrCode(url, QRCodeGenerator.ECCLevel.H);

        //    // Load logo from embedded resource
        //    var assembly = Assembly.GetExecutingAssembly();
        //    var resourceName = "Zambia.Invoice.Assets.icon-192x192.png";

        //    Bitmap? logo = null;
        //    using var stream = assembly.GetManifestResourceStream(resourceName);
        //    if (stream != null)
        //    {
        //        logo = (Bitmap)Image.FromStream(stream);
        //    }

        //    using var qrCode = new QRCode(qrCodeData);
        //    using var qrBitmap = qrCode.GetGraphic(20, Color.Black, Color.White, logo, 15, 6, true);

        //    using var ms = new MemoryStream();
        //    qrBitmap.Save(ms, System.Drawing.Imaging.ImageFormat.Png);

        //    logo?.Dispose();

        //    return $"data:image/png;base64,{Convert.ToBase64String(ms.ToArray())}";
        //}
    }
}