using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;
using Zambia.Invoice.Models;
using Zambia.Invoice.Models.Context;

namespace Zambia.Invoice.Services
{
    public class InvoiceService
    {
        private string baseUrl = "http://localhost:8080";
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly ConfigurationService _configService;
        private readonly IServiceProvider _serviceProvider;

        public InvoiceService(IDbContextFactory<AppDbContext> contextFactory, ConfigurationService configService, IServiceProvider serviceProvider)
        {
            _contextFactory = contextFactory;
            _configService = configService;
            _serviceProvider = serviceProvider;
        }

        public async Task<InvoiceResponseModel> SaveInvoiceAPI_Async(InvoiceCreateModel request)
        {
            try
            {
                var apiService = new ApiService(baseUrl);
                var credentials = await _configService.GetCredentialsAsync();

                InvoiceResponseModel invoiceResponse = await apiService.PostAsync<InvoiceCreateModel, InvoiceResponseModel>(
                    "sandboxvsdc/trnsSales/saveSales",
                    request);

                // Save to database
                //await SaveInvoiceToDatabase(request, invoiceResponse);

                return invoiceResponse;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error saving invoice: {ex.Message}", ex);
            }
        }

        private async Task SaveInvoiceToDatabase(InvoiceCreateModel request, InvoiceResponseModel response)
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // Create InvoiceResponseEntity for database
                var invoiceResponseEntity = new InvoiceResponseEntity
                {
                    Id = Guid.NewGuid(),
                    resultCd = response.resultCd ?? string.Empty,
                    resultMsg = response.resultMsg ?? string.Empty,
                    resultDt = response.resultDt ?? string.Empty,

                    // Map ReturnData properties
                    rcptNo = response.data?.rcptNo ?? 0,
                    intrlData = response.data?.intrlData ?? string.Empty,
                    rcptSign = response.data?.rcptSign ?? string.Empty,
                    vsdcRcptPbctDate = response.data?.vsdcRcptPbctDate ?? string.Empty,
                    sdcId = response.data?.sdcId ?? string.Empty,
                    mrcNo = response.data?.mrcNo ?? string.Empty,
                    qrCodeUrl = response.data?.qrCodeUrl ?? string.Empty
                };

                // Add to context and save
                context.InvoiceResponses.Add(invoiceResponseEntity);
                await context.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error saving invoice to database: {ex.Message}", ex);
            }
        }

        //Create a method to get all invoices from  the database
        public async Task<List<InvoiceResponseEntity>> GetAllInvoicesDB_Async()
        {
            //return a list of all invoices from the database
            using var context = _contextFactory.CreateDbContext();
            List<InvoiceResponseEntity> invoices = await context.InvoiceResponses.ToListAsync();

            return invoices;
        }

        public async Task<List<InvoiceResponseEntity>> GetAllCompletedInvoicesDB_Async()
        {
            //return a list of all invoices from the database
            using var context = _contextFactory.CreateDbContext();
            List<InvoiceResponseEntity> invoices = await context.InvoiceResponses
                .Where(w=>w.resultCd == "000" && !String.IsNullOrEmpty( w.invoiceId)).ToListAsync();

            return invoices;
        }

    }
}
