﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zambia.Invoice.Models;
using Zambia.Invoice.Models.Context;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Zambia.Invoice.Services
{
    public class ItemService
    {
        private string baseUrl = "http://localhost:8080";
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly ConfigurationService _configService;

        public ItemService(IDbContextFactory<AppDbContext> contextFactory, ConfigurationService configService)
        {
            _contextFactory = contextFactory;
            _configService = configService;
        }

        private async Task<ItemResponse> LoadAllItemsAsync()
        {
            var apiService = new ApiService(baseUrl);
            var credentials = await _configService.GetCredentialsAsync();

            ItemResponse itemsResponse = await apiService.PostAsync<object, ItemResponse>(
                "sandboxvsdc/items/selectItems",
                new
                {
                    tpin = credentials.tpin,
                    bhfId = credentials.bhfId,
                    dvcSrlNo = credentials.dvcSrlNo,
                    lastReqDt = credentials.lastReqDt
                });

            return itemsResponse;
        }

        public async Task<ItemResponseEntity> LoadAndSaveAllItemsAsync()
        {
            try
            {
                // Load data from API
                var itemsResponse = await LoadAllItemsAsync();

                //Jacques TODO --> this should be dynamic probably...
                var validNames = new[] { "Flighting", "Production", "Rental" };
                List<ItemList> newList = new List<ItemList>();
                foreach (var item in itemsResponse.data.itemList)
                {
                    //only get items from valid names
                    if (validNames.Contains(item.itemNm))
                    {
                        newList.Add(item);
                    }
                }
                itemsResponse.data.itemList = newList;
                //////////////////////////////////////
                ///

                // Save to database
                var savedItems = await SaveToDatabase(itemsResponse);

                return savedItems;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error loading and saving all items data: {ex.Message}", ex);
            }
        }

        private async Task<ItemResponseEntity> SaveToDatabase(ItemResponse itemsResponse)
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // Create ItemResponseEntity for database
                var itemResponseEntity = new ItemResponseEntity
                {
                    Id = Guid.NewGuid(),
                    resultCd = itemsResponse.resultCd ?? string.Empty,
                    resultMsg = itemsResponse.resultMsg ?? string.Empty,
                    resultDt = itemsResponse.resultDt ?? string.Empty,
                    itemList = new List<ItemListEntity>()
                };

                // Process the itemList from the API response
                if (itemsResponse.data?.itemList != null)
                {
                    foreach (var item in itemsResponse.data.itemList)
                    {
                        var itemEntity = new ItemListEntity
                        {
                            Id = Guid.NewGuid(),
                            ItemResponseEntityId = itemResponseEntity.Id,
                            tpin = item.tpin?.ToString() ?? string.Empty,
                            bhfId = item.bhfId ?? string.Empty,
                            itemCd = item.itemCd ?? string.Empty,
                            itemClsCd = item.itemClsCd ?? string.Empty,
                            itemTyCd = item.itemTyCd ?? string.Empty,
                            itemNm = item.itemNm ?? string.Empty,
                            itemStdNm = item.itemStdNm ?? string.Empty,
                            orgnNatCd = item.orgnNatCd ?? string.Empty,
                            pkgUnitCd = item.pkgUnitCd ?? string.Empty,
                            qtyUnitCd = item.qtyUnitCd ?? string.Empty,
                            vatCatCd = item.vatCatCd ?? string.Empty,
                            iplCatCd = item.iplCatCd?.ToString() ?? string.Empty,
                            tlCatCd = item.tlCatCd?.ToString() ?? string.Empty,
                            exciseTxCatCd = item.exciseTxCatCd?.ToString() ?? string.Empty,
                            btchNo = item.btchNo?.ToString() ?? string.Empty,
                            regBhfId = item.regBhfId ?? string.Empty,
                            bcd = item.bcd?.ToString() ?? string.Empty,
                            dftPrc = item.dftPrc,
                            addInfo = item.addInfo?.ToString() ?? string.Empty,
                            sftyQty = item.sftyQty,
                            manufactuterTpin = item.manufactuterTpin ?? string.Empty,
                            manufacturerItemCd = item.manufacturerItemCd ?? string.Empty,
                            rrp = item.rrp,
                            svcChargeYn = item.svcChargeYn ?? string.Empty,
                            rentalYn = item.rentalYn ?? string.Empty,
                            useYn = item.useYn ?? string.Empty,
                            regrNm = item.regrNm ?? string.Empty,
                            regrId = item.regrId ?? string.Empty,
                            modrNm = item.modrNm ?? string.Empty,
                            modrId = item.modrId ?? string.Empty
                        };

                        itemResponseEntity.itemList.Add(itemEntity);
                    }
                }

                // Add to context and save
                context.ItemResponses.Add(itemResponseEntity);
                await context.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();

                // Return the saved entity with all relationships loaded
                var savedEntity = await context.ItemResponses
                    .Include(i => i.itemList)
                    .FirstOrDefaultAsync(i => i.Id == itemResponseEntity.Id);

                return savedEntity ?? itemResponseEntity;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error saving items data to database: {ex.Message}", ex);
            }
        }

        // Method to retrieve saved data from database
        public async Task<List<ItemResponseEntity>> GetAllItemsFromDatabase()
        {
            //Jacques TODO --> this should be dynamic probably...
            var validNames = new[] { "Flighting", "Production", "Rental" };
            using var context = _contextFactory.CreateDbContext();
            var data = await context.ItemResponses
                .Include(i => i.itemList)
                .ToListAsync();

            // filter children in memory
            data.ForEach(i =>
                i.itemList = i.itemList
                    .Where(c => validNames.Contains(c.itemNm))
                    .ToList());

            return data;
        }

        // Method to retrieve and reconstruct the original structure
        public async Task<ItemResponse> GetItemResponseAsync(Guid itemResponseEntityId)
        {
            using var context = _contextFactory.CreateDbContext();
            var entity = await context.ItemResponses
                .Include(ire => ire.itemList)
                .FirstOrDefaultAsync(ire => ire.Id == itemResponseEntityId);

            if (entity == null) return null;

            return new ItemResponse
            {
                resultCd = entity.resultCd,
                resultMsg = entity.resultMsg,
                resultDt = entity.resultDt,
                data = new DataItem
                {
                    itemList = entity.itemList.Select(i => new ItemList
                    {
                        tpin = i.tpin,
                        bhfId = i.bhfId,
                        itemCd = i.itemCd,
                        itemClsCd = i.itemClsCd,
                        itemTyCd = i.itemTyCd,
                        itemNm = i.itemNm,
                        itemStdNm = i.itemStdNm,
                        orgnNatCd = i.orgnNatCd,
                        pkgUnitCd = i.pkgUnitCd,
                        qtyUnitCd = i.qtyUnitCd,
                        vatCatCd = i.vatCatCd,
                        iplCatCd = i.iplCatCd,
                        tlCatCd = i.tlCatCd,
                        exciseTxCatCd = i.exciseTxCatCd,
                        btchNo = i.btchNo,
                        regBhfId = i.regBhfId,
                        bcd = i.bcd,
                        dftPrc = i.dftPrc,
                        addInfo = i.addInfo,
                        sftyQty = i.sftyQty,
                        manufactuterTpin = i.manufactuterTpin,
                        manufacturerItemCd = i.manufacturerItemCd,
                        rrp = i.rrp,
                        svcChargeYn = i.svcChargeYn,
                        rentalYn = i.rentalYn,
                        useYn = i.useYn,
                        regrNm = i.regrNm,
                        regrId = i.regrId,
                        modrNm = i.modrNm,
                        modrId = i.modrId
                    }).ToList()
                }
            };
        }

        // Method to delete all items data
        public async Task<bool> DeleteAllItemsDataAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var itemListCount = await context.ItemLists.CountAsync();
                if (itemListCount > 0)
                {
                    await context.Database.ExecuteSqlRawAsync("DELETE FROM [ZRA].[ItemLists]");
                }

                ////Why delete the ItemResponses?
                var responseCount = await context.ItemResponses.CountAsync();
                if (responseCount > 0)
                {
                    await context.Database.ExecuteSqlRawAsync("DELETE FROM [ZRA].[ItemResponses]");
                }

                await transaction.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error deleting all items data: {ex.Message}", ex);
            }
        }

        // Method to get count of records
        public async Task<(int ResponseCount, int ItemCount)> GetRecordCountsAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            var responseCount = await context.ItemResponses.CountAsync();
            var itemCount = await context.ItemLists.CountAsync();

            return (responseCount, itemCount);
        }

        // Create individual item
        public async Task<ItemResponseEntity> CreateItemAsync(ItemListEntity itemEntity)
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                itemEntity.Id = Guid.NewGuid();

                context.ItemLists.Add(itemEntity);
                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return await context.ItemResponses
                    .Include(i => i.itemList)
                    .FirstOrDefaultAsync(i => i.itemList.Any(il => il.Id == itemEntity.Id));
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error creating item: {ex.Message}", ex);
            }
        }

        // Update individual item
        public async Task<ItemListEntity> UpdateItemAsync(Guid itemId, ItemListEntity updatedItem)
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var existingItem = await context.ItemLists.FindAsync(itemId);
                if (existingItem == null)
                    throw new Exception($"Item with ID {itemId} not found");

                // Update all properties
                existingItem.tpin = updatedItem.tpin;
                existingItem.bhfId = updatedItem.bhfId;
                existingItem.itemCd = updatedItem.itemCd;
                existingItem.itemClsCd = updatedItem.itemClsCd;
                existingItem.itemTyCd = updatedItem.itemTyCd;
                existingItem.itemNm = updatedItem.itemNm;
                existingItem.itemStdNm = updatedItem.itemStdNm;
                existingItem.orgnNatCd = updatedItem.orgnNatCd;
                existingItem.pkgUnitCd = updatedItem.pkgUnitCd;
                existingItem.qtyUnitCd = updatedItem.qtyUnitCd;
                existingItem.vatCatCd = updatedItem.vatCatCd;
                existingItem.iplCatCd = updatedItem.iplCatCd;
                existingItem.tlCatCd = updatedItem.tlCatCd;
                existingItem.exciseTxCatCd = updatedItem.exciseTxCatCd;
                existingItem.btchNo = updatedItem.btchNo;
                existingItem.regBhfId = updatedItem.regBhfId;
                existingItem.bcd = updatedItem.bcd;
                existingItem.dftPrc = updatedItem.dftPrc;
                existingItem.addInfo = updatedItem.addInfo;
                existingItem.sftyQty = updatedItem.sftyQty;
                existingItem.manufactuterTpin = updatedItem.manufactuterTpin;
                existingItem.manufacturerItemCd = updatedItem.manufacturerItemCd;
                existingItem.rrp = updatedItem.rrp;
                existingItem.svcChargeYn = updatedItem.svcChargeYn;
                existingItem.rentalYn = updatedItem.rentalYn;
                existingItem.useYn = updatedItem.useYn;
                existingItem.regrNm = updatedItem.regrNm;
                existingItem.regrId = updatedItem.regrId;
                existingItem.modrNm = updatedItem.modrNm;
                existingItem.modrId = updatedItem.modrId;

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return existingItem;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error updating item: {ex.Message}", ex);
            }
        }

        // Delete individual item
        public async Task<bool> DeleteItemAsync(Guid itemId)
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var item = await context.ItemLists.FindAsync(itemId);
                if (item == null)
                    return false;

                context.ItemLists.Remove(item);
                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error deleting item: {ex.Message}", ex);
            }
        }

        // Get individual item by ID
        public async Task<ItemListEntity> GetItemByIdAsync(Guid itemId)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.ItemLists.FindAsync(itemId);
        }

        public async Task<List<ItemListEntity>> GetItemList()
        {
            using var context = _contextFactory.CreateDbContext();
           
            var data = await context.ItemLists.ToListAsync();

            return data;

        }
    }
}
