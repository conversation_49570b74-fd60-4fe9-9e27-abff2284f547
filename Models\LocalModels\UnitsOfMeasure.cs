﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Models.LocalModels
{
    public static class UnitsOfMeasure
    {
        public static List<DropDownValueText> UnitsOfMeasureList = new List<DropDownValueText>
    {
        new DropDownValueText { Value = "4B", Text = "Pair" },
        new DropDownValueText { Value = "AV", Text = "Cap" },
        new DropDownValueText { Value = "BA", Text = "Barrel" },
        new DropDownValueText { Value = "BE", Text = "bundle" },
        new DropDownValueText { Value = "BG", Text = "bag" },
        new DropDownValueText { Value = "BL", Text = "block" },
        new DropDownValueText { Value = "BLL", Text = "Barrel (petroleum) (158,987 dm3)" },
        new DropDownValueText { Value = "BX", Text = "box" },
        new DropDownValueText { Value = "CA", Text = "Can" },
        new DropDownValueText { Value = "CEL", Text = "Cell" },
        new DropDownValueText { Value = "CMT", Text = "centimetre" },
        new DropDownValueText { Value = "CR", Text = "CARAT" },
        new DropDownValueText { Value = "DR", Text = "Drum" },
        new DropDownValueText { Value = "DZ", Text = "Dozen" },
        new DropDownValueText { Value = "GLL", Text = "Gallon" },
        new DropDownValueText { Value = "GRM", Text = "Gram" },
        new DropDownValueText { Value = "GRO", Text = "Gross" },
        new DropDownValueText { Value = "KG", Text = "Kilo-Gramme" },
        new DropDownValueText { Value = "KTM", Text = "kilometre" },
        new DropDownValueText { Value = "KWT", Text = "kilowatt" },
        new DropDownValueText { Value = "L", Text = "Litre" },
        new DropDownValueText { Value = "LBR", Text = "pound" },
        new DropDownValueText { Value = "LK", Text = "link" },
        new DropDownValueText { Value = "LTR", Text = "Litre" },
        new DropDownValueText { Value = "M", Text = "Metre" },
        new DropDownValueText { Value = "M2", Text = "Square Metre" },
        new DropDownValueText { Value = "M3", Text = "Cubic Metre" },
        new DropDownValueText { Value = "MGM", Text = "milligram" },
        new DropDownValueText { Value = "MTR", Text = "metre" },
        new DropDownValueText { Value = "MWT", Text = "megawatt hour (1000 kW.h)" },
        new DropDownValueText { Value = "NO", Text = "Number" },
        new DropDownValueText { Value = "NX", Text = "part per thousand" },
        new DropDownValueText { Value = "PA", Text = "packet" },
        new DropDownValueText { Value = "PG", Text = "plate" },
        new DropDownValueText { Value = "PR", Text = "pair" },
        new DropDownValueText { Value = "RL", Text = "reel" },
        new DropDownValueText { Value = "RO", Text = "roll" },
        new DropDownValueText { Value = "SET", Text = "set" },
        new DropDownValueText { Value = "ST", Text = "sheet" },
        new DropDownValueText { Value = "TNE", Text = "tonne (metric ton)" },
        new DropDownValueText { Value = "TU", Text = "tube" },
        new DropDownValueText { Value = "U", Text = "Pieces/item [Number]" },
        new DropDownValueText { Value = "YRD", Text = "yard" },
        new DropDownValueText { Value = "P1", Text = "Pack" },
        new DropDownValueText { Value = "PL", Text = "Pallet" },
        new DropDownValueText { Value = "Ft", Text = "Feet" },
        new DropDownValueText { Value = "MM", Text = "Millimetre" },
        new DropDownValueText { Value = "In", Text = "Inches" },
        new DropDownValueText { Value = "Oz", Text = "Ounce" },
        new DropDownValueText { Value = "YR", Text = "Year" },
        new DropDownValueText { Value = "M", Text = "Month" },
        new DropDownValueText { Value = "Wk", Text = "Week" },
        new DropDownValueText { Value = "D", Text = "Day" },
        new DropDownValueText { Value = "hr", Text = "Hour" },
        new DropDownValueText { Value = "ha", Text = "Hectare" },
        new DropDownValueText { Value = "yd2", Text = "Square yards" },
        new DropDownValueText { Value = "ft2", Text = "Square feet" },
        new DropDownValueText { Value = "cm2", Text = "Square centimetre" },
        new DropDownValueText { Value = "m2", Text = "Square metre" },
        new DropDownValueText { Value = "pt", Text = "Pints" },
        new DropDownValueText { Value = "qt", Text = "Quarts" },
        new DropDownValueText { Value = "mm", Text = "Millilitre" },
        new DropDownValueText { Value = "2X", Text = "Meter/Minute" },
        new DropDownValueText { Value = "4G", Text = "Microliter" },
        new DropDownValueText { Value = "4O", Text = "Microfarad" },
        new DropDownValueText { Value = "4T", Text = "Pikofarad" },
        new DropDownValueText { Value = "A", Text = "Ampere" },
        new DropDownValueText { Value = "A87", Text = "Gigaohm" },
        new DropDownValueText { Value = "A93", Text = "Gram/Cubic meter" },
        new DropDownValueText { Value = "ACR", Text = "Acre" },
        new DropDownValueText { Value = "B34", Text = "Kilogram/cubic decimeter" },
        new DropDownValueText { Value = "B45", Text = "Kilomol" },
        new DropDownValueText { Value = "B47", Text = "Kilonewton" },
        new DropDownValueText { Value = "B73", Text = "Meganewton" },
        new DropDownValueText { Value = "B75", Text = "Megohm" },
        new DropDownValueText { Value = "B78", Text = "Megavolt" },
        new DropDownValueText { Value = "B84", Text = "Microampere" },
        new DropDownValueText { Value = "BAG", Text = "Bag" },
        new DropDownValueText { Value = "BAR", Text = "bar" },
        new DropDownValueText { Value = "BOT", Text = "Bottle" },
        new DropDownValueText { Value = "BQK", Text = "Becquerel/kilogram" },
        new DropDownValueText { Value = "C10", Text = "Millifarad" },
        new DropDownValueText { Value = "C36", Text = "Mol per cubic meter" },
        new DropDownValueText { Value = "C38", Text = "Mol per liter" },
        new DropDownValueText { Value = "C39", Text = "Nanoampere" },
        new DropDownValueText { Value = "C3S", Text = "Cubic centimeter/second" },
        new DropDownValueText { Value = "C41", Text = "Nanofarad" },
        new DropDownValueText { Value = "C56", Text = "Newton/Square millimeter" },
        new DropDownValueText { Value = "CCM", Text = "Cubic centimeter" },
        new DropDownValueText { Value = "CD", Text = "Candela" },
        new DropDownValueText { Value = "CDM", Text = "Cubic decimeter" },
        new DropDownValueText { Value = "EA", Text = "Each" }
    };
    }
}
