﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Zambia.Invoice.Forms.ItemForms;
using Zambia.Invoice.Models;

namespace Zambia.Invoice.Forms.InvoiceForms
{
    public partial class frmInvoice : Form
    {

        private readonly IServiceProvider _serviceProvider;
        private List<ItemListEntity> _selectedItems = new List<ItemListEntity>();

        public frmInvoice(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            InitializeComponent();
            this.Load += frmInvoice_Load;
        }

        private void cmb_salesTyCd_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void grbOptional_Enter(object sender, EventArgs e)
        {

        }

        private void dateTimePicker4_ValueChanged(object sender, EventArgs e)
        {

        }

        private void frmInvoice_Load(object sender, EventArgs e)
        {
            // Set default selections for combo boxes
            cmb_rcptTyCd.SelectedIndex = 0; // "Sales"
            cmb_pmtTyCd.SelectedIndex = 0;  // "Cash"
            cmb_salesSttsCd.SelectedIndex = 0; // "Pending"

            // cmb_prchrAcptcYn remains unselected (SelectedIndex = -1)
            cmb_prchrAcptcYn.SelectedIndex = -1;
        }

        private void btn_AddItem_Click(object sender, EventArgs e)
        {
            using (var selectItemForm = _serviceProvider.GetRequiredService<frmSelectItem>())
            {
                if (selectItemForm.ShowDialog() == DialogResult.OK)
                {
                    // Get selected items from SelectItemForm
                    var selectedItems = selectItemForm.SelectedItems;

                    // Add them to our list (avoiding duplicates)
                    foreach (var item in selectedItems)
                    {
                        if (!_selectedItems.Any(x => x.Id == item.Id))
                        {
                            _selectedItems.Add(item);
                        }
                    }

                    // Update the DataGridView with selected items
                    dgv_Items.DataSource = null;
                    dgv_Items.DataSource = _selectedItems.ToList();

                    if (dgv_Items.Columns.Count > 0)
                    {
                        dgv_Items.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                    }
                }
            }
        }

        private void btn_CreateInvoice_Click(object sender, EventArgs e)
        {
            // PAGE 46/42 on VSDC

            InvoiceCreateModel create = new InvoiceCreateModel();
            // REQUIRED START
            create.tpin = txt_tpin.Text; 
            create.cisInvcNo = txt_cisInvcNo.Text;
            create.bhfId = txt_bhfId.Text; 
            create.salesTyCd = cmb_salesTyCd.Text; 
            create.rcptTyCd = cmb_rcptTyCd.Text; 
            create.pmtTyCd = cmb_pmtTyCd.Text; 
            create.salesSttsCd = cmb_salesSttsCd.Text; 
            create.salesDt = dtp_salesDt.Text; 
            create.totItemCnt = Convert.ToInt32(txt_totItemCnt.Text);
            create.totTaxblAmt = 0; // I NEED TO WORK THIS OUT
            create.totTaxAmt = 0; // I NEED TO WORK THIS OUT
            create.totAmt = 0; // I NEED TO WORK THIS OUT
            create.regrId = txt_regrId.Text;
            create.regrNm = txt_regrNm.Text;
            create.modrId = txt_modrId.Text;
            create.modrNm = txt_modrNm.Text;
            create.currencyTyCd = txt_currencyTyCd.Text;
            create.exchangeRt = txt_exchangeRt.Text;
            create.cfmDt = dtp_cfmDt.Text;

            // REQUIRED END


            // OPTIONAL START

            create.orgInvcNo = Convert.ToInt32( txt_orgIncNo.Text);
            create.custTpin = txt_custTpin.Text; 
            create.custNm = txt_custNm.Text; 
            create.stockRlsDt = dtp_stockRlsDt.Text; 
            create.cnclReqDt = dtp_cnclReqDt.Text;
            create.cnclDt = dtp_cnclDt.Text;
            create.rfdDt = dtp_rfdDt.Text;
            create.rfdRsnCd = txt_rfdRsnCd.Text;
            create.invcAdjustReason = txt_invcAdjustReason.Text;
            create.taxbl

            create.dbtRsnCd = txt_dbtRsnCd.Text; // required
            // required
            create.lpoNumber = txt_lpoNumber.Text; // required
          
            
            
            //
            create.destnCountryCd = txt_destnCountryCd.Text; // required
            create.saleCtyCd = txt_saleCtyCd.Text; // required
            create.whsId = txt_whsId.Text; // required
         
            create.cashDcAmt = 0; // check this // optional
            create.cashDcRt = 0; // check this 
            // optional
   
            
            create.cnclRsnCd = txt_cnclRsnCd.Text;
            create.crtDt = dtp_crtDt.Text; // required
            create.crtId = txt_crtId.Text; // required
            create.dlvyAddr = txt_dlvyAddr.Text;
            create.dlvyCt = txt_dlvyCt.Text;
            create.dlvyDt = dtp_dlvyDt.Text;
            create.dlvyNm = txt_dlvyNm.Text;
            create.dlvySt = txt_dlvySt.Text;
            create.dlvyTelNo = txt_dlvyTelNo.Text;
            create.email = txt_email.Text;
            create.invcDt = dtp_invcDt.Text; // required
            create.invcNo = txt_invcNo.Text; // required
            create.invcSttsCd = cmb_invcSttsCd.Text; // required
            create.invcTyCd = cmb_invcTyCd.Text; // required
            create.memo = txt_memo.Text;
            create.modDt = dtp_modDt.Text; // required
            create.modId = txt_modId.Text; // required
            create.pmtDueDt = dtp_pmtDueDt.Text; // required
            create.prchrAcptcYn = cmb_prchrAcptcYn.Text; // required
            create.telNo = txt_telNo.Text;
            
            create.totQty = 0; // check this // required


            //create.taxblAmtA = 0;
            //create.taxblAmtB = 0;
            //create.taxblAmtC1 =0;
            //create.taxblAmtC2 =0;
            //create.taxblAmtC3 =0;
            //create.taxblAmtD =0;
            //create.taxblAmtE = 0;
            //create.taxblAmtF = 0;
            //create.taxblAmtRvat = 0;
            //create.taxblAmtIpl1 = 0;
            //create.taxblAmtIpl2 = 0;
            //create.taxblAmtTl = 0;
            //create.taxblAmtEcm = 0;
            //create.taxblAmtExeeg = 0;
            //create.taxblAmtTot = 0;


            //create.taxAmtA = 0;
            //create.taxAmtB = 0;
            //create.taxAmtC1 = 0;
            //create.taxAmtC2 = 0;
            //create.taxAmtC3 = 0;
            //create.taxAmtD = 0;
            //create.taxAmtE = 0;
            //create.taxAmtF = 0;
            //create.taxAmtRvat = 0;
            //create.taxAmtIpl1 = 0;
            //create.taxAmtIpl2 = 0;
            //create.taxAmtTl = 0;
            //create.taxAmtEcm = 0;
            //create.taxAmtTot = 0;
            //
            
            //create.totAmt = 0;




        }

        private void label11_Click(object sender, EventArgs e)
        {

        }

        private void label23_Click(object sender, EventArgs e)
        {

        }
    }
}
