using System.ComponentModel.DataAnnotations;

namespace Zambia.Invoice.Models
{
    public class ReturnData
    {
        public int rcptNo { get; set; }
        public string intrlData { get; set; }
        public string rcptSign { get; set; }
        public string vsdcRcptPbctDate { get; set; }
        public string sdcId { get; set; }
        public string mrcNo { get; set; }
        public string qrCodeUrl { get; set; }
    }

    public class InvoiceResponseModel
    {
        public string resultCd { get; set; }
        public string resultMsg { get; set; }
        public string resultDt { get; set; }
        public ReturnData data { get; set; }
    }

    public class InvoiceResponseEntity
    {
        [Key]
        public Guid Id { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string CIS { get; set; } = "";
        public string resultCd { get; set; } = "";
        public string resultMsg { get; set; } = "";
        public string resultDt { get; set; } = "";

        // ReturnData properties flattened
        public int rcptNo { get; set; }
        public string intrlData { get; set; } = "";
        public string rcptSign { get; set; } = "";
        public string vsdcRcptPbctDate { get; set; } = "";
        public string sdcId { get; set; } = "";
        public string mrcNo { get; set; } = "";
        public string qrCodeUrl { get; set; } = "";

        public string invoiceId { get; set; } = "";

        public string qrCodeImage { get; set; } = "";
    }
}
