﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Zambia.Invoice.Models
{
    // POCO classes for JSON deserialization only - NOT mapped to database
    public class DataItem
    {
        public List<ItemList> itemList { get; set; }
    }

    public class ItemList
    {
        public object tpin { get; set; }
        public string bhfId { get; set; }
        public string itemCd { get; set; }
        public string itemClsCd { get; set; }
        public string itemTyCd { get; set; }
        public string itemNm { get; set; }
        public string itemStdNm { get; set; }
        public string orgnNatCd { get; set; }
        public string pkgUnitCd { get; set; }
        public string qtyUnitCd { get; set; }
        public string vatCatCd { get; set; }
        public object iplCatCd { get; set; }
        public object tlCatCd { get; set; }
        public object exciseTxCatCd { get; set; }
        public object btchNo { get; set; }
        public string regBhfId { get; set; }
        public object bcd { get; set; }
        public int dftPrc { get; set; }
        public object addInfo { get; set; }
        public int sftyQty { get; set; }
        public string manufactuterTpin { get; set; }
        public string manufacturerItemCd { get; set; }
        public decimal? rrp { get; set; }
        public string svcChargeYn { get; set; }
        public string rentalYn { get; set; }
        public string useYn { get; set; }
        public string regrNm { get; set; }
        public string regrId { get; set; }
        public string modrNm { get; set; }
        public string modrId { get; set; }
    }

    // This ItemResponse is for deserialization - will have a separate DB entity
    public class ItemResponse
    {
        public string resultCd { get; set; }
        public string resultMsg { get; set; }
        public string resultDt { get; set; }
        public DataItem data { get; set; }
    }

    // Database entities below (separate from deserialization classes):

    public class ItemResponseEntity
    {
        [Key]
        public Guid Id { get; set; } // Primary Key
        public string resultCd { get; set; }
        public string resultMsg { get; set; }
        public string resultDt { get; set; }

        // Navigation property - one ItemResponseEntity can have many ItemListEntity
        public List<ItemListEntity> itemList { get; set; }
    }

    public class ItemListEntity
    {
        [Key]
        public Guid Id { get; set; } // Primary Key
        public string tpin { get; set; }
        public string bhfId { get; set; }
        public string itemCd { get; set; }
        public string itemClsCd { get; set; }
        public string itemTyCd { get; set; }
        public string itemNm { get; set; }
        public string itemStdNm { get; set; }
        public string orgnNatCd { get; set; }
        public string pkgUnitCd { get; set; }
        public string qtyUnitCd { get; set; }
        public string vatCatCd { get; set; }
        public string iplCatCd { get; set; }
        public string tlCatCd { get; set; }
        public string exciseTxCatCd { get; set; }
        public string btchNo { get; set; }
        public string regBhfId { get; set; }
        public string bcd { get; set; }
        public int dftPrc { get; set; }
        public string addInfo { get; set; }
        public int sftyQty { get; set; }
        public string manufactuterTpin { get; set; }
        public string manufacturerItemCd { get; set; }
        public decimal? rrp { get; set; }
        public string svcChargeYn { get; set; }
        public string rentalYn { get; set; }
        public string useYn { get; set; }
        public string regrNm { get; set; }
        public string regrId { get; set; }
        public string modrNm { get; set; }
        public string modrId { get; set; }

        // Foreign Key to ItemResponseEntity
        public Guid ItemResponseEntityId { get; set; }
        [ForeignKey("ItemResponseEntityId")]
        public ItemResponseEntity ItemResponse { get; set; }
    }

    public class SaveItemRequest
    {
        [Key]
        public Guid Id { get; set; } // Primary Key

        // Foreign Key to ItemResponseEntity
        public Guid ItemResponseEntityId { get; set; }
        [ForeignKey("ItemResponseEntityId")]
        public ItemResponseEntity ItemResponse { get; set; }

        public string Tpin { get; set; }
        public string BhfId { get; set; }
        public string DvcSrlNo { get; set; }
        public string LastReqDt { get; set; }
        public string ItemCd { get; set; }
        public string ItemClsCd { get; set; }
        public string ItemTyCd { get; set; }
        public string ItemNm { get; set; }
        public decimal? DftPrc { get; set; }
        public string ManufacturerTpin { get; set; }
        public string ManufacturerItemCd { get; set; }
        public decimal? Rrp { get; set; }
        public string SvcChargeYn { get; set; }
        public string RentalYn { get; set; }
        public string AddInfo { get; set; }
        public int? SftyQty { get; set; }
        public string IsrcAplcbYn { get; set; }
        public string UseYn { get; set; }
        public string RegrNm { get; set; }
        public string RegrId { get; set; }
        public string ModrNm { get; set; }
        public string ModrId { get; set; }
        public string Test { get; set; }
    }
}
