﻿
SELECT TOP (1000) c.[Id]
      ,c.[resultCd]
      ,c.[resultMsg]
      ,c.[resultDt]
	  ,l.[Id] as AAAAAA
      ,l.[cdCls]
      ,l.[cdClsNm]
      ,l.[userDfnNm1]
      ,l.[CodesModelId]
	   ,d.[Id]
      ,d.[cd]
      ,d.[cdNm]
      ,d.[userDfnCd1]
      ,d.[ClsListId]
  FROM [ZambiaInvoice].[dbo].[CodesModels] c 
  inner join [ZambiaInvoice].[dbo].[ClsLists] l ON c.[Id] = l.[CodesModelId]
  inner join [ZambiaInvoice].[dbo].[DtlLists] d on l.[Id] =d.[ClsListId] order by AAAAAA