﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms.Design;
using Zambia.Invoice.Global;
using Zambia.Invoice.Models;
using Zambia.Invoice.Models.Context;

namespace Zambia.Invoice.Services
{
    public class CodeService
    {
        private string baseUrl = "http://localhost:8080";
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly ConfigurationService _configService;

        public CodeService(IDbContextFactory<AppDbContext> contextFactory, ConfigurationService configService)
        {
            _contextFactory = contextFactory;
            _configService = configService;
        }


        private async Task<CodesModel> LoadDataAsync()
        {
            var apiService = new ApiService(baseUrl);
            var credentials = await _configService.GetCredentialsAsync();

            CodesModel codesResponse = await apiService.PostAsync<object, CodesModel>(
                "sandboxvsdc/code/selectCodes",
                new
                {
                    tpin = credentials.tpin,
                    bhfId = GlobalConfig.BranchCode,
                    dvcSrlNo = GlobalConfig.DeviceSerialNumber,
                    lastReqDt = credentials.lastReqDt
                });

            return codesResponse;
        }
        public async Task<CodesModel> LoadAndSaveDataAsync()
        {
            try
            {
                // Load data from API
                var codesResponse = await LoadDataAsync();

                // Save to database
                var savedCodes = await SaveToDatabase(codesResponse);

                return savedCodes;
            }
            catch (Exception ex)
            {
                // Log the exception or handle as needed
                throw new Exception($"Error loading and saving data: {ex.Message}", ex);
            }
        }

        private async Task<CodesModel> SaveToDatabase(CodesModel codesResponse)
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // Step 1: Create the main CodesModel entity
                // EF Core will automatically generate a new Guid for the PK
                var codesModel = new CodesModel
                {
                    Id = Guid.NewGuid(), // Generate new Primary Key
                    resultCd = codesResponse.resultCd ?? string.Empty,
                    resultMsg = codesResponse.resultMsg ?? string.Empty,
                    resultDt = codesResponse.resultDt ?? string.Empty,
                    ClsList = new List<ClsList>() // Initialize the navigation property
                };

                // Step 2: Process the ClsList from the API response
                if (codesResponse.data?.clsList != null)
                {
                    foreach (var clsItem in codesResponse.data.clsList)
                    {
                        // Create ClsList entity with FK reference
                        var clsListEntity = new ClsList
                        {
                            Id = Guid.NewGuid(), // Generate new Primary Key
                            cdCls = clsItem.cdCls ?? string.Empty,
                            cdClsNm = clsItem.cdClsNm ?? string.Empty,
                            userDfnNm1 = clsItem.userDfnNm1 ?? string.Empty,
                            CodesModelId = codesModel.Id, // Set Foreign Key
                            dtlList = new List<DtlList>() // Initialize the navigation property
                        };

                        // Step 3: Process the DtlList for each ClsList
                        if (clsItem.dtlList != null)
                        {
                            foreach (var dtlItem in clsItem.dtlList)
                            {
                                var dtlListEntity = new DtlList
                                {
                                    Id = Guid.NewGuid(), // Generate new Primary Key
                                    cd = dtlItem.cd ?? string.Empty,
                                    cdNm = dtlItem.cdNm ?? string.Empty,
                                    userDfnCd1 = dtlItem.userDfnCd1 ?? string.Empty,
                                    ClsListId = clsListEntity.Id // Set Foreign Key
                                };

                                clsListEntity.dtlList.Add(dtlListEntity);
                            }
                        }

                        codesModel.ClsList.Add(clsListEntity);
                    }
                }

                // Step 4: Add to context and save
                context.CodesModels.Add(codesModel);
                await context.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();

                // Step 5: Return the saved entity with all relationships loaded
                var savedEntity = await context.CodesModels
                    .Include(c => c.ClsList)
                        .ThenInclude(cl => cl.dtlList)
                    .FirstOrDefaultAsync(c => c.Id == codesModel.Id);

                return savedEntity ?? codesModel;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error saving to database: {ex.Message}", ex);
            }
        }



        // Method to retrieve saved data from database
        public async Task<List<CodesModel>> GetAllCodesFromDatabase()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.CodesModels
                .Include(c => c.ClsList)
                    .ThenInclude(cl => cl.dtlList)
                .ToListAsync();
        }



        // Method to delete everything from the database
        public async Task<bool> DeleteAllDataAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // Delete in correct order due to foreign key constraints
                // Child records first, then parent records

                // Step 1: Delete all DtlList records (child of ClsList)
                var dtlListCount = await context.DtlLists.CountAsync();
                if (dtlListCount > 0)
                {
                    await context.Database.ExecuteSqlRawAsync("DELETE FROM [ZRA].[DtlLists]");
                }

                // Step 2: Delete all ClsList records (child of CodesModel)
                var clsListCount = await context.ClsLists.CountAsync();
                if (clsListCount > 0)
                {
                    await context.Database.ExecuteSqlRawAsync("DELETE FROM [ZRA].[ClsLists]");
                }

                // Step 3: Delete all CodesModel records (parent)
                var codesModelCount = await context.CodesModels.CountAsync();
                if (codesModelCount > 0)
                {
                    await context.Database.ExecuteSqlRawAsync("DELETE FROM [ZRA].[CodesModels]");
                }

                // Step 4: Reset identity columns if needed (optional)
                // Note: Since we're using Guid PKs, this might not be necessary
                // But if you want to reset any auto-increment columns:
                // await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT('TableName', RESEED, 0)");

                // Commit the transaction
                await transaction.CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                // Rollback on error
                await transaction.RollbackAsync();
                throw new Exception($"Error deleting all data: {ex.Message}", ex);
            }
        }

        // Alternative method using EF Core entities (slower but safer)
        public async Task<bool> DeleteAllDataWithEntitiesAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // Load all data with relationships
                var allCodesModels = await context.CodesModels
                    .Include(c => c.ClsList)
                        .ThenInclude(cl => cl.dtlList)
                    .ToListAsync();

                // Remove all entities (EF Core will handle cascade delete if configured)
                context.CodesModels.RemoveRange(allCodesModels);

                // Save changes
                await context.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error deleting all data with entities: {ex.Message}", ex);
            }
        }

        // Method to delete specific CodesModel and all its children
        public async Task<bool> DeleteCodesModelAsync(Guid codesModelId)
        {
            using var context = _contextFactory.CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var codesModel = await context.CodesModels
                    .Include(c => c.ClsList)
                        .ThenInclude(cl => cl.dtlList)
                    .FirstOrDefaultAsync(c => c.Id == codesModelId);

                if (codesModel == null)
                {
                    return false; // Not found
                }

                // Remove the entity (cascade delete will handle children if configured)
                context.CodesModels.Remove(codesModel);
                await context.SaveChangesAsync();

                await transaction.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"Error deleting CodesModel: {ex.Message}", ex);
            }
        }

        // Method to get count of records (useful before deletion)
        public async Task<(int CodesCount, int ClsListCount, int DtlListCount)> GetRecordCountsAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            var codesCount = await context.CodesModels.CountAsync();
            var clsListCount = await context.ClsLists.CountAsync();
            var dtlListCount = await context.DtlLists.CountAsync();

            return (codesCount, clsListCount, dtlListCount);
        }


    }
}
