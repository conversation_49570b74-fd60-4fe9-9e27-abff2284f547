﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net9.0-windows</TargetFramework>
		<Nullable>enable</Nullable>
		<UseWindowsForms>true</UseWindowsForms>
		<ImplicitUsings>enable</ImplicitUsings>
		<ApplicationHighDpiMode>SystemAware</ApplicationHighDpiMode>
		<ForceDesignerDpiUnaware>true</ForceDesignerDpiUnaware>
		<ApplicationIcon>Assets\PRIMEICON.ico</ApplicationIcon>
	</PropertyGroup>

	<ItemGroup>
		<EmbeddedResource Include="Assets\icon-192x192.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
		<EmbeddedResource Include="Assets\PRIMEICON.ico">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
		<Compile Remove="Forms\InvoiceForms\InvoiceForm - Copy.cs" />
		<Compile Remove="Forms\InvoiceForms\InvoiceForm - Copy.Designer.cs" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Remove="Forms\InvoiceForms\InvoiceForm - Copy.resx" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="Assets\icon-192x192.png" />
	</ItemGroup>

	<ItemGroup>
	  <Content Include="Assets\PRIMEICON.ico" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.9" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.9" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.9">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.9" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="QRCoder" Version="1.6.0" />
	</ItemGroup>

	<ItemGroup>
		<Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Migrations\" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Properties\Resources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Resources.resx</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="Properties\Resources.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>Resources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>

</Project>