﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Zambia.Invoice.Forms
{
    public partial class LoadingForm : Form
    {
        private System.Windows.Forms.Timer? spinnerTimer;
        private float currentAngle = 0;
        private const int SPINNER_SIZE = 40;
        public LoadingForm()
        {
            InitializeComponent();
            // Form styling
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Size = new Size(300, 150);
            this.BackColor = Color.White;
            this.DoubleBuffered = true;

            // Add shadow/border effect
            this.Padding = new Padding(3);

            // Create loading text
            var label = new Label
            {
                Text = "Loading...",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(64, 64, 64),
                AutoSize = true
            };

            // Center the label
            label.Location = new Point(
                (this.ClientSize.Width - label.Width) / 2,
                (this.ClientSize.Height - label.Height) / 2 + 30
            );
            this.Controls.Add(label);

            // Set up spinner animation
            spinnerTimer = new System.Windows.Forms.Timer();
            spinnerTimer.Interval = 30; // 30ms for smooth animation
            spinnerTimer.Tick += SpinnerTimer_Tick;
            spinnerTimer.Start();

            // Handle paint event for custom drawing
            this.Paint += LoadingForm_Paint;
        }
        private void SpinnerTimer_Tick(object? sender, EventArgs e)
        {
            // Update spinner angle
            currentAngle = (currentAngle + 10) % 360;
            this.Invalidate(); // Force redraw
        }

        private void LoadingForm_Paint(object? sender, PaintEventArgs e)
        {
            // Enable high quality rendering
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

            // Draw border/shadow
            using var borderPen = new Pen(Color.FromArgb(20, 0, 0, 0), 3);
            e.Graphics.DrawRectangle(borderPen, 1, 1, this.Width - 3, this.Height - 3);

            // Draw spinner
            int centerX = this.ClientSize.Width / 2;
            int centerY = (this.ClientSize.Height / 2) - 20;
            Rectangle spinnerRect = new Rectangle(
                centerX - SPINNER_SIZE / 2,
                centerY - SPINNER_SIZE / 2,
                SPINNER_SIZE,
                SPINNER_SIZE
            );

            // Draw spinner segments with gradient opacity
            for (int i = 0; i < 12; i++)
            {
                float angle = currentAngle + (i * 30);
                int alpha = 255 - (i * 20); // Fade out each segment
                if (alpha < 40) alpha = 40; // Minimum opacity

                using var spinnerPen = new Pen(Color.FromArgb(alpha, 0, 120, 215), 3);
                e.Graphics.DrawArc(spinnerPen, spinnerRect, angle, 15);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Clean up timer when form closes
            spinnerTimer?.Stop();
            spinnerTimer?.Dispose();
            base.OnFormClosing(e);
        }

        private void LoadingForm_Load(object sender, EventArgs e)
        {

        }
    }
}
