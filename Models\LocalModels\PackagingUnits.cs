﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zambia.Invoice.Models.LocalModels
{
    public static class PackagingUnits
    {
        public static List<DropDownValueText> PackagingUnitList = new List<DropDownValueText>
    {
        new DropDownValueText { Value = "AM", Text = "Ampoule" },
        new DropDownValueText { Value = "BA", Text = "Barrel" },
        new DropDownValueText { Value = "BC", Text = "Bottle crate" },
        new DropDownValueText { Value = "BE", Text = "Bundle" },
        new DropDownValueText { Value = "BF", Text = "Balloon, non-protected" },
        new DropDownValueText { Value = "BG", Text = "Bag" },
        new DropDownValueText { Value = "BJ", Text = "Bucket" },
        new DropDownValueText { Value = "BK", Text = "Basket" },
        new DropDownValueText { Value = "BL", Text = "Bale" },
        new DropDownValueText { Value = "BQ", Text = "Bottle, protected cylindrical" },
        new DropDownValueText { Value = "BR", Text = "Bar" },
        new DropDownValueText { Value = "BV", Text = "Bottle, bulbous" },
        new DropDownValueText { Value = "BZ", Text = "Bag" },
        new DropDownValueText { Value = "CA", Text = "Can" },
        new DropDownValueText { Value = "CH", Text = "Chest" },
        new DropDownValueText { Value = "CJ", Text = "Coffin" },
        new DropDownValueText { Value = "CL", Text = "Coil" },
        new DropDownValueText { Value = "CR", Text = "Wooden Box, Wooden Case" },
        new DropDownValueText { Value = "CS", Text = "Cassette" },
        new DropDownValueText { Value = "CT", Text = "Carton" },
        new DropDownValueText { Value = "CTN", Text = "Container" },
        new DropDownValueText { Value = "CY", Text = "Cylinder" },
        new DropDownValueText { Value = "DR", Text = "Drum" },
        new DropDownValueText { Value = "GT", Text = "Extra Countable Item" },
        new DropDownValueText { Value = "HH", Text = "Hand Baggage" },
        new DropDownValueText { Value = "IZ", Text = "Ingots" },
        new DropDownValueText { Value = "JR", Text = "Jar" },
        new DropDownValueText { Value = "JU", Text = "Jug" },
        new DropDownValueText { Value = "JY", Text = "Jerry CAN Cylindrical" },
        new DropDownValueText { Value = "KZ", Text = "Canester" },
        new DropDownValueText { Value = "LZ", Text = "Logs, in bundle/bunch/truss" },
        new DropDownValueText { Value = "NT", Text = "Net" },
        new DropDownValueText { Value = "OU", Text = "Non-Exterior Packaging Unit" },
        new DropDownValueText { Value = "PD", Text = "Poddon" },
        new DropDownValueText { Value = "PG", Text = "Plate" },
        new DropDownValueText { Value = "PI", Text = "Pipe" },
        new DropDownValueText { Value = "PO", Text = "Pilot" },
        new DropDownValueText { Value = "PU", Text = "Traypack" },
        new DropDownValueText { Value = "RL", Text = "Reel" },
        new DropDownValueText { Value = "RO", Text = "Roll" },
        new DropDownValueText { Value = "RZ", Text = "Rods, in bundle/bunch/truss" },
        new DropDownValueText { Value = "SK", Text = "Skeletoncase" },
        new DropDownValueText { Value = "TY", Text = "Tank, cylindrical" },
        new DropDownValueText { Value = "VG", Text = "Bulk,gas(at 1031 mbar 15 oC)" },
        new DropDownValueText { Value = "VL", Text = "Bulk,liquid(at normal temperature/pressure)" },
        new DropDownValueText { Value = "VO", Text = "Bulk, solid, large particles(nodules)" },
        new DropDownValueText { Value = "VQ", Text = "Bulk, gas(liquefied at abnormal temperature/pressure)" },
        new DropDownValueText { Value = "VR", Text = "Bulk, solid, granular particles(grains)" },
        new DropDownValueText { Value = "VT", Text = "Extra Bulk Item" },
        new DropDownValueText { Value = "VY", Text = "Bulk, fine particles(powder)" },
        new DropDownValueText { Value = "ML", Text = "Mills cigarette" },
        new DropDownValueText { Value = "TN", Text = "TAN 1TAN REFER TO 20BAGS" },
        new DropDownValueText { Value = "B/L", Text = "Black Lug" },
        new DropDownValueText { Value = "BIN", Text = "Bin" },
        new DropDownValueText { Value = "BOTT", Text = "Bottle" },
        new DropDownValueText { Value = "BOUQ", Text = "Bouquet" },
        new DropDownValueText { Value = "BOWL", Text = "Bowl" },
        new DropDownValueText { Value = "BOX", Text = "Box" },
        new DropDownValueText { Value = "BUBG", Text = "Budget Bag" },
        new DropDownValueText { Value = "BULK", Text = "Bulk Pack" },
        new DropDownValueText { Value = "BUNC", Text = "Bunch" },
        new DropDownValueText { Value = "BUND", Text = "Bundle" },
        new DropDownValueText { Value = "CLEA", Text = "Clear Lid" },
        new DropDownValueText { Value = "EA", Text = "Each" },
        new DropDownValueText { Value = "EACH", Text = "Each" },
        new DropDownValueText { Value = "ECON", Text = "Economy Bag" },
        new DropDownValueText { Value = "ECPO", Text = "Econo Poc Sell" },
        new DropDownValueText { Value = "G/L", Text = "Green Lugs" },
        new DropDownValueText { Value = "KARR", Text = "Karripoc" },
        new DropDownValueText { Value = "LABE", Text = "Labels" },
        new DropDownValueText { Value = "MESH", Text = "Mesh" },
        new DropDownValueText { Value = "NETL", Text = "Netlon" },
        new DropDownValueText { Value = "P/KG", Text = "Per Kilogram" },
        new DropDownValueText { Value = "PACK", Text = "PACK" },
        new DropDownValueText { Value = "PCRT", Text = "PCRT" },
        new DropDownValueText { Value = "PILP", Text = "Pilpac" },
        new DropDownValueText { Value = "POC", Text = "Pocket" },
        new DropDownValueText { Value = "POCS", Text = "Poc Sell" },
        new DropDownValueText { Value = "POLY", Text = "Poly Bags" },
        new DropDownValueText { Value = "POT", Text = "Pots" },
        new DropDownValueText { Value = "PREP", Text = "Prepack" },
        new DropDownValueText { Value = "PUND", Text = "Pun DTray" },
        new DropDownValueText { Value = "PUNN", Text = "Punnet - Packaging" },
        new DropDownValueText { Value = "SLEE", Text = "Sleeve" },
        new DropDownValueText { Value = "SOCK", Text = "Sock" },
        new DropDownValueText { Value = "TRAY", Text = "Tray" },
        new DropDownValueText { Value = "TRSE", Text = "Tray Sell" },
        new DropDownValueText { Value = "TUB", Text = "TUB" },
        new DropDownValueText { Value = "UNWR", Text = "Unwrap" },
        new DropDownValueText { Value = "WRAP", Text = "Wraped" }
    };
    }
}
