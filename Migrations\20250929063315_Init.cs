﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Zambia.Invoice.Migrations
{
    /// <inheritdoc />
    public partial class Init : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "Z<PERSON>");

            migrationBuilder.CreateSequence<int>(
                name: "ItemCodesSequence",
                schema: "ZRA");

            migrationBuilder.CreateTable(
                name: "CIS_Invoices",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CIS = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CIS_Invoices", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ClassModels",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    resultCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    resultMsg = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    resultDt = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ClassModels", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CodesModels",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    resultCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    resultMsg = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    resultDt = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CodesModels", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "InvoiceResponses",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CIS = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    resultCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    resultMsg = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    resultDt = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    rcptNo = table.Column<int>(type: "int", nullable: false),
                    intrlData = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    rcptSign = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    vsdcRcptPbctDate = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    sdcId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    mrcNo = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    qrCodeUrl = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InvoiceResponses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ItemCodes",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CountryOfOrigin = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductType = table.Column<int>(type: "int", nullable: false),
                    PackagingUnit = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    QuantityUnit = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IncrementValue = table.Column<int>(type: "int", nullable: false, defaultValueSql: "NEXT VALUE FOR [ZRA].[ItemCodesSequence]")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ItemCodes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ItemResponses",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    resultCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    resultMsg = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    resultDt = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ItemResponses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PostBodyModels",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    tpin = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    bhfId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    dvcSrlNo = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    lastReqDt = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PostBodyModels", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ItemClsLists",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    itemClsCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    itemClsNm = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    itemClsLvl = table.Column<int>(type: "int", nullable: false),
                    taxTyCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    mjrTgYn = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    useYn = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ClassModelEntityId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ItemClsLists", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ItemClsLists_ClassModels_ClassModelEntityId",
                        column: x => x.ClassModelEntityId,
                        principalSchema: "ZRA",
                        principalTable: "ClassModels",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ClsLists",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    cdCls = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    cdClsNm = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    userDfnNm1 = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CodesModelId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ClsLists", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ClsLists_CodesModels_CodesModelId",
                        column: x => x.CodesModelId,
                        principalSchema: "ZRA",
                        principalTable: "CodesModels",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ItemLists",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    tpin = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    bhfId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    itemCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    itemClsCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    itemTyCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    itemNm = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    itemStdNm = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    orgnNatCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    pkgUnitCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    qtyUnitCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    vatCatCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    iplCatCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    tlCatCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    exciseTxCatCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    btchNo = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    regBhfId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    bcd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    dftPrc = table.Column<int>(type: "int", nullable: false),
                    addInfo = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    sftyQty = table.Column<int>(type: "int", nullable: false),
                    manufactuterTpin = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    manufacturerItemCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    rrp = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    svcChargeYn = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    rentalYn = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    useYn = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    regrNm = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    regrId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    modrNm = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    modrId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ItemResponseEntityId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ItemLists", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ItemLists_ItemResponses_ItemResponseEntityId",
                        column: x => x.ItemResponseEntityId,
                        principalSchema: "ZRA",
                        principalTable: "ItemResponses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SaveItemRequests",
                schema: "ZRA",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ItemResponseEntityId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Tpin = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    BhfId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DvcSrlNo = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    LastReqDt = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ItemCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ItemClsCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ItemTyCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ItemNm = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DftPrc = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ManufacturerTpin = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ManufacturerItemCd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Rrp = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    SvcChargeYn = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    RentalYn = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AddInfo = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    SftyQty = table.Column<int>(type: "int", nullable: true),
                    IsrcAplcbYn = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    UseYn = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    RegrNm = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    RegrId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ModrNm = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ModrId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Test = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SaveItemRequests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SaveItemRequests_ItemResponses_ItemResponseEntityId",
                        column: x => x.ItemResponseEntityId,
                        principalSchema: "ZRA",
                        principalTable: "ItemResponses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DtlLists",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    cd = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    cdNm = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    userDfnCd1 = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ClsListId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DtlLists", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DtlLists_ClsLists_ClsListId",
                        column: x => x.ClsListId,
                        principalSchema: "ZRA",
                        principalTable: "ClsLists",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ClsLists_CodesModelId",
                schema: "ZRA",
                table: "ClsLists",
                column: "CodesModelId");

            migrationBuilder.CreateIndex(
                name: "IX_DtlLists_ClsListId",
                table: "DtlLists",
                column: "ClsListId");

            migrationBuilder.CreateIndex(
                name: "IX_ItemClsLists_ClassModelEntityId",
                schema: "ZRA",
                table: "ItemClsLists",
                column: "ClassModelEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_ItemLists_ItemResponseEntityId",
                schema: "ZRA",
                table: "ItemLists",
                column: "ItemResponseEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_SaveItemRequests_ItemResponseEntityId",
                schema: "ZRA",
                table: "SaveItemRequests",
                column: "ItemResponseEntityId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CIS_Invoices",
                schema: "ZRA");

            migrationBuilder.DropTable(
                name: "DtlLists");

            migrationBuilder.DropTable(
                name: "InvoiceResponses",
                schema: "ZRA");

            migrationBuilder.DropTable(
                name: "ItemClsLists",
                schema: "ZRA");

            migrationBuilder.DropTable(
                name: "ItemCodes",
                schema: "ZRA");

            migrationBuilder.DropTable(
                name: "ItemLists",
                schema: "ZRA");

            migrationBuilder.DropTable(
                name: "PostBodyModels",
                schema: "ZRA");

            migrationBuilder.DropTable(
                name: "SaveItemRequests",
                schema: "ZRA");

            migrationBuilder.DropTable(
                name: "ClsLists",
                schema: "ZRA");

            migrationBuilder.DropTable(
                name: "ClassModels",
                schema: "ZRA");

            migrationBuilder.DropTable(
                name: "ItemResponses",
                schema: "ZRA");

            migrationBuilder.DropTable(
                name: "CodesModels",
                schema: "ZRA");

            migrationBuilder.DropSequence(
                name: "ItemCodesSequence",
                schema: "ZRA");
        }
    }
}
