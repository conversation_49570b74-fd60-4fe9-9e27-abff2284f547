﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Zambia.Invoice.Models.Context;

#nullable disable

namespace Zambia.Invoice.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.9")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.HasSequence<int>("ItemCodesSequence", "ZRA");

            modelBuilder.Entity("Zambia.Invoice.Models.CIS_InvoiceModel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CIS")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CIS_Invoices", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ClassModelEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("resultCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("resultDt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("resultMsg")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ClassModels", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ClsList", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CodesModelId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("cdCls")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("cdClsNm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("userDfnNm1")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CodesModelId");

                    b.ToTable("ClsLists", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.CodesModel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("resultCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("resultDt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("resultMsg")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CodesModels", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.DtlList", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClsListId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("cd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("cdNm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("userDfnCd1")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ClsListId");

                    b.ToTable("DtlLists");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ExistingTables.ZRA_INVOICE_D", b =>
                {
                    b.Property<double?>("AMTPRIC")
                        .HasColumnType("float");

                    b.Property<double?>("AMTTXBL")
                        .HasColumnType("float");

                    b.Property<string>("IDITEM")
                        .IsRequired()
                        .HasMaxLength(12)
                        .IsUnicode(false)
                        .HasColumnType("varchar(12)");

                    b.Property<string>("INVOICENO")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<string>("INV_TYPE")
                        .IsRequired()
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("varchar(3)");

                    b.Property<int>("QTYINVC")
                        .HasColumnType("int");

                    b.Property<decimal?>("RATETAX1")
                        .HasColumnType("decimal(21, 4)");

                    b.Property<string>("TEXTDESC")
                        .IsRequired()
                        .HasMaxLength(12)
                        .IsUnicode(false)
                        .HasColumnType("varchar(12)");

                    b.Property<double?>("TOTTAX")
                        .HasColumnType("float");

                    b.ToTable((string)null);

                    b.ToView("ZRA_INVOICE_D", (string)null);
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ExistingTables.ZRA_INVOICE_H", b =>
                {
                    b.Property<decimal?>("AMTINVCTOT")
                        .HasColumnType("money");

                    b.Property<decimal?>("AMTTAX1")
                        .HasColumnType("money");

                    b.Property<decimal?>("AMTTAXTOT")
                        .HasColumnType("money");

                    b.Property<string>("CNTBTCH")
                        .IsRequired()
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("varchar(1)");

                    b.Property<string>("CNTITEM")
                        .IsRequired()
                        .HasMaxLength(2)
                        .IsUnicode(false)
                        .HasColumnType("varchar(2)");

                    b.Property<string>("CODECURN")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("DATEINVC")
                        .HasMaxLength(8000)
                        .IsUnicode(false)
                        .HasColumnType("varchar(8000)");

                    b.Property<int>("EXCHRATEHC")
                        .HasColumnType("int");

                    b.Property<string>("IDCUST")
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)");

                    b.Property<string>("IDINVC")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<string>("IDTAXREGI1")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("INVCAPPLTO")
                        .HasMaxLength(21)
                        .HasColumnType("nvarchar(21)");

                    b.Property<int>("LASTLINE")
                        .HasColumnType("int");

                    b.Property<string>("NAMECUST")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("SPECINST")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("TEXTTRX")
                        .HasColumnType("int");

                    b.Property<string>("ZRARCPT")
                        .IsRequired()
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("varchar(1)");

                    b.Property<string>("ZRASDC")
                        .IsRequired()
                        .HasMaxLength(13)
                        .IsUnicode(false)
                        .HasColumnType("varchar(13)");

                    b.ToTable((string)null);

                    b.ToView("ZRA_INVOICE_H", (string)null);
                });

            modelBuilder.Entity("Zambia.Invoice.Models.InvoiceResponseEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CIS")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("intrlData")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("invoiceId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("mrcNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("qrCodeImage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("qrCodeUrl")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("rcptNo")
                        .HasColumnType("int");

                    b.Property<string>("rcptSign")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("resultCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("resultDt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("resultMsg")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("sdcId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("vsdcRcptPbctDate")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("InvoiceResponses", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ItemClsListEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClassModelEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("itemClsCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("itemClsLvl")
                        .HasColumnType("int");

                    b.Property<string>("itemClsNm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("mjrTgYn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("taxTyCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("useYn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ClassModelEntityId");

                    b.ToTable("ItemClsLists", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ItemCodesModel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CountryOfOrigin")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("IncrementValue")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValueSql("NEXT VALUE FOR [ZRA].[ItemCodesSequence]");

                    b.Property<string>("PackagingUnit")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProductType")
                        .HasColumnType("int");

                    b.Property<string>("QuantityUnit")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ItemCodes", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ItemListEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ItemResponseEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("addInfo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("bcd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("bhfId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("btchNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("dftPrc")
                        .HasColumnType("int");

                    b.Property<string>("exciseTxCatCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("iplCatCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("itemCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("itemClsCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("itemNm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("itemStdNm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("itemTyCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("manufacturerItemCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("manufactuterTpin")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("modrId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("modrNm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("orgnNatCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("pkgUnitCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("qtyUnitCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("regBhfId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("regrId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("regrNm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("rentalYn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("rrp")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("sftyQty")
                        .HasColumnType("int");

                    b.Property<string>("svcChargeYn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("tlCatCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("tpin")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("useYn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("vatCatCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ItemResponseEntityId");

                    b.ToTable("ItemLists", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ItemResponseEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("resultCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("resultDt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("resultMsg")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ItemResponses", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.PostBodyModel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("bhfId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("dvcSrlNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("lastReqDt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("tpin")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("PostBodyModels", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.SaveItemRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AddInfo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BhfId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("DftPrc")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("DvcSrlNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IsrcAplcbYn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ItemCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ItemClsCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ItemNm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ItemResponseEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ItemTyCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastReqDt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManufacturerItemCd")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManufacturerTpin")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModrId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModrNm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegrId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegrNm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RentalYn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Rrp")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("SftyQty")
                        .HasColumnType("int");

                    b.Property<string>("SvcChargeYn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Test")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tpin")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UseYn")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ItemResponseEntityId");

                    b.ToTable("SaveItemRequests", "ZRA");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ClsList", b =>
                {
                    b.HasOne("Zambia.Invoice.Models.CodesModel", "CodesModel")
                        .WithMany("ClsList")
                        .HasForeignKey("CodesModelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CodesModel");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.DtlList", b =>
                {
                    b.HasOne("Zambia.Invoice.Models.ClsList", "ClsList")
                        .WithMany("dtlList")
                        .HasForeignKey("ClsListId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ClsList");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ItemClsListEntity", b =>
                {
                    b.HasOne("Zambia.Invoice.Models.ClassModelEntity", "ClassModel")
                        .WithMany("itemClsList")
                        .HasForeignKey("ClassModelEntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ClassModel");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ItemListEntity", b =>
                {
                    b.HasOne("Zambia.Invoice.Models.ItemResponseEntity", "ItemResponse")
                        .WithMany("itemList")
                        .HasForeignKey("ItemResponseEntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ItemResponse");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.SaveItemRequest", b =>
                {
                    b.HasOne("Zambia.Invoice.Models.ItemResponseEntity", "ItemResponse")
                        .WithMany()
                        .HasForeignKey("ItemResponseEntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ItemResponse");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ClassModelEntity", b =>
                {
                    b.Navigation("itemClsList");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ClsList", b =>
                {
                    b.Navigation("dtlList");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.CodesModel", b =>
                {
                    b.Navigation("ClsList");
                });

            modelBuilder.Entity("Zambia.Invoice.Models.ItemResponseEntity", b =>
                {
                    b.Navigation("itemList");
                });
#pragma warning restore 612, 618
        }
    }
}
