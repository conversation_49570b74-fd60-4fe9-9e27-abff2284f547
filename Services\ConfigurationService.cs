using Microsoft.EntityFrameworkCore;
using Zambia.Invoice.Global;
using Zambia.Invoice.Models;
using Zambia.Invoice.Models.Context;

namespace Zambia.Invoice.Services
{
    public class ConfigurationService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private PostBodyModel? _cachedCredentials;

        public ConfigurationService(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<PostBodyModel> GetCredentialsAsync()
        {
            if (_cachedCredentials == null)
            {
                using var context = _contextFactory.CreateDbContext();
                _cachedCredentials = await context.PostBodyModel.FirstOrDefaultAsync();

                // If no credentials exist, create default ones
                if (_cachedCredentials == null)
                {
                    _cachedCredentials = new PostBodyModel
                    {
                        tpin = "1002087869",
                        bhfId = GlobalConfig.BranchCode,
                        dvcSrlNo = GlobalConfig.DeviceSerialNumber,
                        lastReqDt = "20231215000000"
                    };
                }
            }

            return _cachedCredentials;
        }

        public void ClearCache()
        {
            _cachedCredentials = null;
        }
    }
}
