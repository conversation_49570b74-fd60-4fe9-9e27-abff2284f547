﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zambia.Invoice.Models.ExistingTables;

namespace Zambia.Invoice.Models.Context
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        // ===== EXISTING DATABASE VIEWS (From scaffolding) =====
        public virtual DbSet<ZRA_INVOICE_D> ZRA_INVOICE_Ds { get; set; }
        public virtual DbSet<ZRA_INVOICE_H> ZRA_INVOICE_Hs { get; set; }

        public DbSet<PostBodyModel> PostBodyModel { get; set; }

        // First set of entities
        public DbSet<ClsList> ClsLists { get; set; }
        public DbSet<DtlList> DtlLists { get; set; }
        public DbSet<CodesModel> CodesModels { get; set; }

        // Second set of entities (database entities only)
        public DbSet<ClassModelEntity> ClassModel { get; set; }
        public DbSet<ItemClsListEntity> ItemClsList { get; set; }

        // Item entities
        //public DbSet<SaveItemRequest> SaveItemRequests { get; set; }
        public DbSet<ItemResponseEntity> ItemResponses { get; set; }
        public DbSet<ItemListEntity> ItemLists { get; set; }
        public DbSet<ItemCodesModel> ItemCodes { get; set; }

        public DbSet<CIS_InvoiceModel> CIS_Invoices { get; set; }
        public DbSet<InvoiceResponseEntity> InvoiceResponses { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // optionsBuilder.UseSqlServer(@"Server=************\Live;Initial Catalog=ZambiaInvoice;User Id=Zambia;Password=********;MultipleActiveResultSets=True;TrustServerCertificate=True;");
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            
            // ===== CONFIGURE EXISTING VIEWS - EXCLUDE FROM MIGRATIONS START =====
            modelBuilder.Entity<ZRA_INVOICE_D>(entity =>
            {
                // Views are automatically excluded from migrations
                entity.ToView("ZRA_INVOICE_D");
                entity.HasNoKey();

                entity.Property(e => e.IDITEM)
                    .IsRequired()
                    .HasMaxLength(12)
                    .IsUnicode(false);
                entity.Property(e => e.INVOICENO)
                    .IsRequired()
                    .HasMaxLength(9);
                entity.Property(e => e.INV_TYPE)
                    .IsRequired()
                    .HasMaxLength(3)
                    .IsUnicode(false);
                entity.Property(e => e.RATETAX1).HasColumnType("decimal(21, 4)");
                entity.Property(e => e.TEXTDESC)
                    .IsRequired()
                    .HasMaxLength(12)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ZRA_INVOICE_H>(entity =>
            {
                // Views are automatically excluded from migrations
                entity.ToView("ZRA_INVOICE_H");
                entity.HasNoKey();

                entity.Property(e => e.AMTINVCTOT).HasColumnType("money");
                entity.Property(e => e.AMTTAX1).HasColumnType("money");
                entity.Property(e => e.AMTTAXTOT).HasColumnType("money");
                entity.Property(e => e.CNTBTCH)
                    .IsRequired()
                    .HasMaxLength(1)
                    .IsUnicode(false);
                entity.Property(e => e.CNTITEM)
                    .IsRequired()
                    .HasMaxLength(2)
                    .IsUnicode(false);
                entity.Property(e => e.CODECURN)
                    .HasMaxLength(10)
                    .IsUnicode(false);
                entity.Property(e => e.DATEINVC)
                    .HasMaxLength(8000)
                    .IsUnicode(false);
                entity.Property(e => e.IDCUST).HasMaxLength(6);
                entity.Property(e => e.IDINVC)
                    .IsRequired()
                    .HasMaxLength(9);
                entity.Property(e => e.IDTAXREGI1)
                    .IsRequired()
                    .HasMaxLength(20);
                entity.Property(e => e.INVCAPPLTO).HasMaxLength(21);
                entity.Property(e => e.NAMECUST).HasMaxLength(50);
                entity.Property(e => e.SPECINST).HasMaxLength(100);
                entity.Property(e => e.ZRARCPT)
                    .IsRequired()
                    .HasMaxLength(1)
                    .IsUnicode(false);
                entity.Property(e => e.ZRASDC)
                    .IsRequired()
                    .HasMaxLength(13)
                    .IsUnicode(false);
            });
            // ===== CONFIGURE EXISTING VIEWS - EXCLUDE FROM MIGRATIONS END =====

            // Ignore POCO classes used only for deserialization
            modelBuilder.Ignore<Data>();
            modelBuilder.Ignore<DataClasses>();
            modelBuilder.Ignore<ItemResponse>();
            modelBuilder.Ignore<ItemList>();

            // Configure schema for all tables - now using ZRA schema
            modelBuilder.Entity<CodesModel>().ToTable("CodesModels", "ZRA");
            modelBuilder.Entity<ClsList>().ToTable("ClsLists", "ZRA");
            // modelBuilder.Entity<DtlList>().ToTable("DtlLists", "ZRA");

            // Configure schema for Class-related tables - DIFFERENT table names
            modelBuilder.Entity<ClassModelEntity>().ToTable("ClassModels", "ZRA");
            modelBuilder.Entity<ItemClsListEntity>().ToTable("ItemClsLists", "ZRA"); // Different table name

            // Configure schema for Item-related tables
            modelBuilder.Entity<SaveItemRequest>().ToTable("SaveItemRequests", "ZRA");
            modelBuilder.Entity<ItemResponseEntity>().ToTable("ItemResponses", "ZRA");
            modelBuilder.Entity<ItemListEntity>().ToTable("ItemLists", "ZRA");

            // Configure ItemCodesModel
            modelBuilder.Entity<ItemCodesModel>().ToTable("ItemCodes", "ZRA");

            // Configure CIS_InvoiceModel and InvoiceResponseEntity
            modelBuilder.Entity<CIS_InvoiceModel>().ToTable("CIS_Invoices", "ZRA");
            modelBuilder.Entity<InvoiceResponseEntity>().ToTable("InvoiceResponses", "ZRA");

            // Configure PostBodyModel
            modelBuilder.Entity<PostBodyModel>().ToTable("PostBodyModels", "ZRA");

            // CODES relationships
            modelBuilder.Entity<CodesModel>()
                .HasMany(c => c.ClsList)
                .WithOne(cl => cl.CodesModel)
                .HasForeignKey(cl => cl.CodesModelId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ClsList>()
                .HasMany(cl => cl.dtlList)
                .WithOne(d => d.ClsList)
                .HasForeignKey(d => d.ClsListId)
                .OnDelete(DeleteBehavior.Cascade);

            // CLASSES relationships
            modelBuilder.Entity<ClassModelEntity>()
                .HasMany(cme => cme.itemClsList)
                .WithOne(icle => icle.ClassModel)
                .HasForeignKey(icle => icle.ClassModelEntityId)
                .OnDelete(DeleteBehavior.Cascade);

            // ITEMS relationships
            modelBuilder.Entity<ItemResponseEntity>()
                .HasMany(ire => ire.itemList)
                .WithOne(ile => ile.ItemResponse)
                .HasForeignKey(ile => ile.ItemResponseEntityId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<SaveItemRequest>()
                .HasOne(sir => sir.ItemResponse)
                .WithMany()
                .HasForeignKey(sir => sir.ItemResponseEntityId)
                .OnDelete(DeleteBehavior.Cascade);

            // Fix decimal precision warnings
            modelBuilder.Entity<SaveItemRequest>()
                .Property(e => e.DftPrc)
                .HasPrecision(18, 2);

            modelBuilder.Entity<SaveItemRequest>()
                .Property(e => e.Rrp)
                .HasPrecision(18, 2);

            // Create sequence in the ZRA schema
            modelBuilder.HasSequence<int>("ItemCodesSequence", "ZRA")
                .StartsAt(1)
                .IncrementsBy(1);

            // Configure auto-increment for IncrementValue with proper schema reference
            modelBuilder.Entity<ItemCodesModel>()
                .Property(e => e.IncrementValue)
                .ValueGeneratedOnAdd()
                .HasDefaultValueSql("NEXT VALUE FOR [ZRA].[ItemCodesSequence]");
        }
    }
}